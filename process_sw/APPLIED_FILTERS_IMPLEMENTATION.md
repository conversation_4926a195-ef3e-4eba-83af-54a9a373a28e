# Applied Filters Implementation

This document outlines the implementation of applied filters storage and retrieval in the pannel-based system.

## Overview

The system now stores and retrieves applied filters along with panel configurations, ensuring that when panels are saved and later loaded, all applied filters are automatically restored.

## Implementation Details

### Backend Changes

#### 1. Updated Pannel Controller Functions

**`saveIndividualPannel` Function:**
- Added `appliedFilters` parameter to request body
- Stores applied filters in panel configuration JSON
- Default filter structure includes:
  - `selectedColumns`: Column selection state
  - `dateFilter`: Date range filters
  - `panelFilters`: Panel-specific filters
  - `conditionalFilters`: Conditional filters
  - `valueRangeFilters`: Value range filters

**`savePannelFromState` Function:**
- Added `appliedFilters` parameter for saving panels from current state
- Includes same filter structure as individual panel saving

#### 2. Filter Data Structure

```json
{
  "appliedFilters": {
    "selectedColumns": {
      "indices": [1, 2, 3],
      "headers": ["column1", "column2", "column3"]
    },
    "dateFilter": {
      "startDate": "2024-01-01",
      "endDate": "2024-12-31"
    },
    "panelFilters": [
      {
        "id": "filter-1",
        "column": "temperature",
        "operator": ">=",
        "value": 25
      }
    ],
    "conditionalFilters": [
      {
        "id": "filter-2",
        "column": "status",
        "operator": "equals",
        "value": "active"
      }
    ],
    "valueRangeFilters": []
  }
}
```

### Frontend Changes

#### 1. Updated PanelOptionsMenu Component

**Interface Updates:**
- Added `appliedFilters` prop to `PanelOptionsMenuProps`
- Includes all filter types: selectedColumns, dateFilter, panelFilters, conditionalFilters, valueRangeFilters

**Function Updates:**
- `saveIndividualPanel`: Now sends `appliedFilters` in request body
- `savePanelAsPannel`: Includes applied filters when saving panel as pannel
- Default filter structure provided if no filters are applied

#### 2. Updated GridItem Component

**New Functions:**
- `getAppliedFilters()`: Extracts current filter state from component props
- Returns structured filter object matching backend expectations

**Updated PanelOptionsMenu Integration:**
- Passes `appliedFilters={getAppliedFilters()}` to PanelOptionsMenu
- Ensures current filter state is available for saving

#### 3. Updated WorkFlowContainer Component

**Enhanced Filter Loading Logic:**
- Checks for new `appliedFilters` structure in panel configurations
- Falls back to old structure for backward compatibility
- Extracts and applies filters when loading panels

**Filter Application Process:**
1. **Panel-Specific Filters**: Applied to individual panels
2. **Global Filters**: Applied across all panels (selectedColumns, dateFilter, conditionalFilters)
3. **Backward Compatibility**: Supports old filter structure

## Data Flow

### Saving Filters

1. **User Applies Filters**: User selects columns, sets date ranges, adds conditional filters
2. **Panel Save Triggered**: User clicks "Save Panel" or "Save as Pannel"
3. **Filter Extraction**: GridItem extracts current filter state via `getAppliedFilters()`
4. **Backend Storage**: Filters stored in panel configuration JSON under `appliedFilters`
5. **Database Persistence**: Panel configuration with filters saved to `pannels` table

### Loading Filters

1. **Panel Data Fetch**: Frontend requests panel data from `/pannel/view/0`
2. **Filter Extraction**: WorkFlowContainer extracts `appliedFilters` from panel configurations
3. **Filter Application**: Filters applied to respective state variables
4. **UI Update**: Panels render with applied filters
5. **Filter Display**: AppliedFilters component shows active filters

## Filter Types Supported

### 1. Column Selection
- **Purpose**: Select specific columns for display
- **Storage**: `selectedColumns.indices` and `selectedColumns.headers`
- **Application**: Applied to OverviewPanel and other column-aware panels

### 2. Date Range Filter
- **Purpose**: Filter data by date range
- **Storage**: `dateFilter.startDate` and `dateFilter.endDate`
- **Application**: Applied globally across all time-series panels

### 3. Panel-Specific Filters
- **Purpose**: Filters specific to individual panels
- **Storage**: `panelFilters` array with filter objects
- **Application**: Applied only to the specific panel that created them

### 4. Conditional Filters
- **Purpose**: Global conditional filters (column operator value)
- **Storage**: `conditionalFilters` array with filter objects
- **Application**: Applied globally across all panels

### 5. Value Range Filters
- **Purpose**: Filter data by value ranges (zoom selections)
- **Storage**: `valueRangeFilters` array
- **Application**: Applied based on zoom interactions

## Backward Compatibility

The implementation maintains backward compatibility with existing panel configurations:

### Old Structure Support
```json
{
  "configuration": {
    "panelFilters": { /* old filter structure */ },
    "selectedColumns": { /* old column selection */ }
  }
}
```

### New Structure
```json
{
  "configuration": {
    "appliedFilters": {
      "selectedColumns": { /* new structure */ },
      "dateFilter": { /* new structure */ },
      "panelFilters": [ /* new structure */ ],
      "conditionalFilters": [ /* new structure */ ],
      "valueRangeFilters": [ /* new structure */ ]
    }
  }
}
```

### Migration Logic
- Frontend checks for `appliedFilters` first
- Falls back to old structure if new structure not found
- Ensures existing panels continue to work without modification

## Benefits

### 1. Complete Filter Persistence
- All applied filters are saved with panel configurations
- No filter state is lost when refreshing or reloading

### 2. Consistent User Experience
- Panels load with the exact same filters that were applied when saved
- Users can continue working from where they left off

### 3. Filter Isolation
- Panel-specific filters remain isolated to their panels
- Global filters apply consistently across all panels

### 4. Comprehensive Filter Support
- Supports all filter types used in the application
- Extensible structure for future filter types

## Usage Examples

### Saving Panel with Filters
```javascript
// User applies filters
setSelectedColumns({ indices: [1, 2], headers: ['temp', 'pressure'] });
setDateFilter({ startDate: '2024-01-01', endDate: '2024-12-31' });

// User saves panel - filters automatically included
const appliedFilters = {
  selectedColumns: { indices: [1, 2], headers: ['temp', 'pressure'] },
  dateFilter: { startDate: '2024-01-01', endDate: '2024-12-31' },
  panelFilters: [],
  conditionalFilters: [],
  valueRangeFilters: []
};
```

### Loading Panel with Filters
```javascript
// Panel configuration loaded from backend
const panelConfig = {
  appliedFilters: {
    selectedColumns: { indices: [1, 2], headers: ['temp', 'pressure'] },
    dateFilter: { startDate: '2024-01-01', endDate: '2024-12-31' }
  }
};

// Filters automatically applied
setSelectedColumns(panelConfig.appliedFilters.selectedColumns);
setDateFilter(panelConfig.appliedFilters.dateFilter);
```

## Future Enhancements

1. **Filter Templates**: Save common filter combinations as templates
2. **Filter Sharing**: Share filter configurations between users
3. **Advanced Filters**: Support for more complex filter operations
4. **Filter History**: Track filter changes over time
5. **Filter Validation**: Validate filters against data schema

## Testing Recommendations

1. **Save and Load**: Test saving panels with various filter combinations and verify they load correctly
2. **Backward Compatibility**: Test loading old panel configurations without `appliedFilters`
3. **Filter Types**: Test each filter type individually and in combination
4. **Edge Cases**: Test with empty filters, invalid filter values, and missing filter properties
5. **Performance**: Test with large numbers of filters and complex filter combinations

## Conclusion

The applied filters implementation provides comprehensive filter persistence for the pannel-based system. Users can now save panels with their complete filter state and have those filters automatically restored when the panels are loaded, providing a seamless and consistent user experience.
