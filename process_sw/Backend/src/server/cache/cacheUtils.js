import redisClient from './redisClient.js';
import stringify from 'fast-json-stable-stringify';
import crypto from 'crypto';

export const getCache = async (key) => {
    const value = await redisClient.get(key);
    return value ? JSON.parse(value) : null;
};

export const setCache = async (key, value) => {
    const expiration = process.env.PORT || 60 * 60; // 1 hour
    await redisClient.set(key, JSON.stringify(value),
        { EX: expiration },
    );
};

export const generateCacheKey = (payload, prefix = '') => {
    const str = stringify(payload);
    return `${prefix}${crypto.createHash('sha256').update(str).digest('hex')}`;
};
