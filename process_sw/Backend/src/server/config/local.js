import 'dotenv/config'
const config= {
	logging: false,
	seed: true,
	postgres: {
		host: process.env.DB_HOST,
		port: 5432,
		user: process.env.POSTGRES_USER,
		password: process.env.POSTGRES_PASSWORD,
		database: process.env.POSTGRES_DB,
		max: 100, // max number of clients in the pool,
		idleTimeoutMillis: 30000 // how long a client is allowed to remain idle before being closed
	}
};
export default config
