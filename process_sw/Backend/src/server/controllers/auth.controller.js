import crypto from 'crypto';
import { sendOtpEmail } from '../utils/mailer.js';
import otpStore from '../utils/otpStore.js';
import User from '../models/user.model.js';
import ActivityLog from '../models/activityLogs.model.js';
import { generateToken } from "../middlewares/jwt.js";
import Roles from '../models/roles.model.js';
import { generateResponse } from "../utils/commonResponse.js";

export const sendOtp = async (req, res) => {
  const { email ,userData} = req.body;
  console.log('userData :', userData);
  if (!email) return res.status(400).json({ message: 'Email is required' });

  const otp = crypto.randomInt(100000, 999999).toString();
  otpStore.storeOtp(email, otp);

  try {
    await sendOtpEmail(email, otp ,userData);
    res.json({ message: 'OTP sent to email' });
  } catch (err) {
    res.status(500).json({ message: 'Failed to send OTP', error: err.message });
  }
};

export const verifyEmailOtp = async (req, res) => {
  const {userId, email, otp } = req.body;
  console.log('userId :', userId);
  if (!email || !otp) return res.status(400).json({ message: 'Email and OTP are required' });

  if (otpStore.verifyOtp(email, otp)) {
    if (!userId) {
      return generateResponse(res, 400, 'User ID and verification token are required');
    }
    
    // Get the user with their 2FA secret
    const user = await User.findOne({
      attributes: ['id', 'first_name', 'last_name', 'email', 'onboarding', 'tenant_id', 'selected_systems', 'two_factor_secret', 'two_factor_enabled'],
      where: { id: userId },
      include: [
        {
          model: Roles,
          as: 'roles',
          attributes: ['name', 'alias', 'rank'] 
        },
      ]
    });
    
    if (!user) {
      return generateResponse(res, 404, 'User not found');
    }
  
    // If verified, create user data and token
    const userData = {
      first_name: user.get('first_name'),
      last_name: user.get('last_name'),
      email: user.get('email'),
      id: user.get('id'),
      onboarding: user.get('onboarding'),
      tenant_id: user.get('tenant_id'),
      selected_systems: user.get('selected_systems'),
      role: user.get('roles')
    };
    
    const jwtToken = generateToken(userData);
    if (!jwtToken) {
      return generateResponse(res, 404, 'Unable to generate token');
    }
    
    userData.token = jwtToken;
    
    // Log the activity
    const newEntry = { user_id: userData.id, last_login: new Date() };
    const activityLog = new ActivityLog(newEntry);
    await activityLog.save();
    
    // Set cookie and return response
    res.cookie('token', jwtToken, { httpOnly: false, secure: false });
    return generateResponse(res, 200, 'OTP verified successfully', userData);
  } else {
    res.status(400).json({ message: 'Invalid or expired OTP', verified: false });
  }
};
