import { sequelize } from "../database/dbConnection.js";
import bucketizationlValueData from "../models/bucketizationValueData.model.js";
import { generateResponse } from "../utils/commonResponse.js";
import { attachedGenericIdentifier } from "./workflow.controllers.js";
export const saveStatisticalData = async (req, res) => {

    try {
        const {id} = req.user
        const {name , workflow_id ,filter_id , bucketization_statistical_value} = req.body
        // console.log('bucketization_statistical_value :', bucketization_statistical_value);
        const inputMaterial = await attachedGenericIdentifier(req.user.tenant_id)
        const uniqueInputMaterials = [...new Set(
            Object.values(inputMaterial)
              .flatMap(system => system.InputMaterial ? Object.keys(system.InputMaterial) : [])
          )];
 
        const saveStatisticalData = await bucketizationlValueData.create({
            user_id : id,
            name : name,
            workflow_id : workflow_id,
            filter_id : filter_id || null,
            bucketization_statistical_value : bucketization_statistical_value,
            input_material_type : uniqueInputMaterials
        })
        return generateResponse(res, 200, 'bucketization statistical data saved successfully', saveStatisticalData)
    } catch (error) {
    console.log('error :', error);
        return generateResponse(res, 500, 'Failed to save bucketization statistical data', error)
    }
};
export const getStatisticalData = async (req, res) => {

    try {
        const {id} = req.user
        // const {name , workflow_id , filter_id , bucketization_statistical_value} = req.body
        const StatisticalData = await bucketizationlValueData.findAll({
            where: { user_id: id }, 
            order: [
                ['id', 'DESC']
            ]
        })
        return generateResponse(res, 200, 'Bucketization statistical data fetched successfully', StatisticalData)
    } catch (error) {
        return generateResponse(res, 500, 'Failed to save bucketization statistical data', error)
    }
};

export const getStatisticalDataInputMaterialWise = async (req, res) => {

    try {
        const {id} = req.user
       const results = await bucketizationlValueData.findAll({
            where: { user_id: id },
            attributes: [
                'input_material_type',
                [sequelize.fn('json_agg', sequelize.literal('"bucketization_value_data"')), 'data']
            ],
            group: ['input_material_type'],
            raw: true,
            order: [
                ['input_material_type', 'ASC']  // or any other valid field for sorting
              ]
        }); 
        
        return generateResponse(res, 200, 'Bucketization statistical data fetched successfully', results)
    } catch (error) {
        console.log("Bucketization statistical data fetched error",error)
        return generateResponse(res, 500, 'Failed to save bucketization statistical data', error)
    }
};