import MLJobData from '../models/MLJobData.model.js';
import axios from 'axios';
import { generateResponse } from "../utils/commonResponse.js";
export const sendMessage = async (req, res) => {
    try {
        const mlJobDataResult = await MLJobData.findOne({
            workflow_id: req.body.workflowId,
        }).sort({ created_at: -1 });
        
        const result = mlJobDataResult?.result?.data;

        let payload ={'question':req.body.content, 'data':result}

        const response = await axios.post('http://52.28.172.168:7070/api/r/llm_correlations_qa',payload);

        return generateResponse(res, 200, 'Chat message sended suceessfully', response?.data)
    } catch (error) {
        console.error('Error creating folder:', error.message);
        return generateResponse(res, 500, 'Failed to fetch response from chatbot', error.message)
    }
};

  
  
