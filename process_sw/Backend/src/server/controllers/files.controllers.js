import { sequelize } from "../database/dbConnection.js";
import { generateResponse } from "../utils/commonResponse.js";
import path from "path";
import fs from 'fs';
import CSVFilesSchema from "../models/file.model.js"
import User from "../models/user.model.js";
import csv from 'csv-parser';
import crypto from 'crypto';
import bcrypt from 'bcrypt';
import WorkflowComponents from "../models/workflowComponents.model.js";
import CsvMapping from "../models/csvMapping.model.js";
import { createObjectCsvWriter } from 'csv-writer';
import 'dotenv/config';
import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand, HeadObjectCommand, ListObjectsV2Command, DeleteObjectsCommand } from "@aws-sdk/client-s3";
import { uploadFileToS3, deleteFile, listFiles, getFileStream } from "../s3 Services/s3Services.js";
import { v4 as uuidv4 } from 'uuid';
import Tenants from "../models/tenants.model.js";
import Systems from "../models/systems.model.js";
import { Op, literal } from "sequelize";
import axios from 'axios';
import MLJob from "../models/MLjobs.model.js";
import mongoose from 'mongoose';


// const s3 = new S3Client({
//   region: process.env.AWS_REGION,
//   credentials: {
//     accessKeyId: process.env.AWS_ACCESS_KEY_ID,
//     secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
//   },
// });

//Common function for material processing
const processMaterialFile = async (fileUrl, fileKeyToSave, fileTypes) => {
  try {
    console.log("@@@Processing material file");
    
    const mlJobId = new mongoose.Types.ObjectId();

    const baseUrlPath = fileUrl.substring(0, fileUrl.lastIndexOf('/'));
    const modelLoadPath = `${baseUrlPath}/model`;
    
    // Create ML job with initial payload
    const mlJobPayload = {
      operation: "identification_model",
      clustering: {},
      datasource: {
        file_path: fileUrl,
        file_save_path: fileUrl,
        feature_filters: {},
        model_load_path: modelLoadPath,
        exclude_features: [],
      },
      workflow_id: 0,
      execution_id: mlJobId.toString(),
      dimensionality_reduction: {},
    };
    console.log("@@@Mljob payload", mlJobPayload);

    //Save ML job to MongoDB with same ObjectId
    const mlJob = await MLJob.create({
      _id: mlJobId,
      workflow_id: 0,
      settings: mlJobPayload,
      actual_settings: {},
      ml_response: {},
      workflow_type: "material_file",
      status: "pending",
      file_id: fileKeyToSave,
    });

    //Call material clustering API
    const response = await axios.post(
      `${process.env.AIML_API}/input_material_clustering_inference`,
      mlJobPayload
    );
    console.log("@@@Material clustering API response", response);
    
    return true;
  } catch (error) {
    console.error('Error in material processing:', error);
    return false;
  }
};

const uploadFile = async (req, res) => {
    const {id} = req.user
    const {tenant_id} = req.user

    try {
      console.log('req.file', req.file)
      const systems = Array.isArray(req.body.systems) ? req.body.systems: JSON.parse(req.body.systems || '[]');
        const filePath = req.file.path;
        // const fileKey = `${req.file.originalname}`;
        let systemNames = []
        if(systems && systems.length){
          systemNames = systems.map(item => item.systemName);
        }
        const folderName = systemNames.join(" + ");

        let fileKey = uuidv4();
        const fileKeyToSave = fileKey

        let tenantData = await Tenants.findOne({ where: {id: tenant_id}})
        let awsCredentials = tenantData.dataValues.aws_credentials
        console.log('---------------',awsCredentials['AWS_FOLDER'])
        if(awsCredentials['AWS_FOLDER'] && awsCredentials['AWS_FOLDER'].trim() !== ''){
          fileKey = `${awsCredentials['AWS_FOLDER']}/${folderName?folderName+'/':""}${fileKey}`

        }else{
          fileKey = `data/data=file/${folderName?folderName+'/':""}${fileKey}`

        }
        const fileUrl = await uploadFileToS3(filePath, fileKey, awsCredentials);

        if(fileUrl){
          // Remove the file from local storage
          fs.unlink(filePath, (err) => {
            if (err) {
              console.error('Error removing file from local storage:', err);
            } else {
              console.log('File removed from local storage:', filePath);
            }
          });
          
          let systemIds = [];
          if(systems && systems.length){
            systemIds = systems.map(item => item.systemId);
          }
          const compatibility = req.body.compatibility;
          
          // Parse the type_of_file from string to array if needed
          let fileTypes;
          try {
            fileTypes = Array.isArray(req.body.type_of_file) 
              ? req.body.type_of_file 
              : JSON.parse(req.body.type_of_file || '["processes"]');
          } catch (e) {
            fileTypes = ['processes']; // Default if parsing fails
          }
          
          const uploadedFile = await CSVFilesSchema.create({
              user_id: id,
              file_name: req.file.originalname,
              path_for_aiml: process.env.FILE_PATH_FOR_AIML + req.file.filename,
              file_path: fileKey,
              description: req.body.description || null,
              version: req.body.version || 1,
              tenant_id:tenant_id,
              systems_id : systemIds,
              compatibility: (id==1 || id ==46) ? true : compatibility,  // id 46 is of kalprish statically added compatibility true
              csv_id: fileKeyToSave,
              aws_file_link: fileUrl,
              parent_file_id: req.body.parent_file_id || null,
              type_of_file: fileTypes,
              clustering_data_response: (fileTypes.includes('material') && compatibility == 'true') ? 'in_progress' : null,
          });

          //Check if file type contains 'material' and create ML job
          console.log("@@@File types", fileTypes.includes('material') && compatibility=='true');
          
          if (fileTypes.includes('material') && compatibility=='true') {
            console.log("@@@@@File type is material and compatibility is", compatibility);
            
            await processMaterialFile(fileUrl, fileKeyToSave, fileTypes);
          }
          
          const fileResponse = {
              csvId: uploadedFile.get('csv_id'),
          }
          return generateResponse(res, 200, 'File uploaded successfully.', uploadedFile)
        }
        else{
          return generateResponse(res, 500, 'Failed to upload file.')
        }


    } catch (error) {
        console.error('error :', error);
        return generateResponse(res, 500, 'Failed to upload file.')
    }
}

// This function will return the csv file each colum,ns data

const getFile = async (req, res) => {
  try {
    const data =[
      {
          "vatNo": "2",
          "sequenceNo": "2",
          "recipeNo": "1",
          "DateTime": "2025-04-15 07:56:56",
          "fillingStopTime": "2122",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "300",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2778",
          "coagulationStartTime": "3095",
          "coagulationStopTime": "5171",
          "cuttingStartTime": "5247",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5516",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5813",
          "cookingStopTime": "7932",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9823",
          "emptyingStopTime": "12211",
          "residenceTimeInVat": "7701",
          "totalProcesstimeInVat": "12211",
          "coagulationDuration": "2076",
          "cookingDuration": "2119",
          "emptyingDuration": "2388",
          "vatpH": "6.47"
      },
      {
          "vatNo": "3",
          "sequenceNo": "3",
          "recipeNo": "1",
          "DateTime": "2025-04-15 08:32:49",
          "fillingStopTime": "1973",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "269",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2616",
          "coagulationStartTime": "3036",
          "coagulationStopTime": "5103",
          "cuttingStartTime": "5179",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5352",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5743",
          "cookingStopTime": "7941",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9816",
          "emptyingStopTime": "11717",
          "residenceTimeInVat": "7843",
          "totalProcesstimeInVat": "11717",
          "coagulationDuration": "2067",
          "cookingDuration": "2198",
          "emptyingDuration": "1901",
          "vatpH": "6.48"
      },
      {
          "vatNo": "4",
          "sequenceNo": "4",
          "recipeNo": "1",
          "DateTime": "2025-04-15 09:05:42",
          "fillingStopTime": "2006",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "293",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2599",
          "coagulationStartTime": "2948",
          "coagulationStopTime": "5060",
          "cuttingStartTime": "5077",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5253",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5657",
          "cookingStopTime": "7710",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9556",
          "emptyingStopTime": "11869",
          "residenceTimeInVat": "7550",
          "totalProcesstimeInVat": "11869",
          "coagulationDuration": "2112",
          "cookingDuration": "2053",
          "emptyingDuration": "2313",
          "vatpH": "6.46"
      },
      {
          "vatNo": "5",
          "sequenceNo": "5",
          "recipeNo": "1",
          "DateTime": "2025-04-15 09:39:08",
          "fillingStopTime": "2006",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "31.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "293",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2677",
          "coagulationStartTime": "3054",
          "coagulationStopTime": "5074",
          "cuttingStartTime": "5150",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5317",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5720",
          "cookingStopTime": "7773",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9659",
          "emptyingStopTime": "11955",
          "residenceTimeInVat": "7653",
          "totalProcesstimeInVat": "11955",
          "coagulationDuration": "2020",
          "cookingDuration": "2053",
          "emptyingDuration": "2296",
          "vatpH": "6.48"
      },
      {
          "vatNo": "6",
          "sequenceNo": "6",
          "recipeNo": "1",
          "DateTime": "2025-04-15 10:14:03",
          "fillingStopTime": "2018",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "32.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "293",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2604",
          "coagulationStartTime": "3018",
          "coagulationStopTime": "5090",
          "cuttingStartTime": "5151",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5339",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5725",
          "cookingStopTime": "7783",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9674",
          "emptyingStopTime": "13238",
          "residenceTimeInVat": "7656",
          "totalProcesstimeInVat": "13238",
          "coagulationDuration": "2072",
          "cookingDuration": "2058",
          "emptyingDuration": "3564",
          "vatpH": "6.47"
      },
      {
          "vatNo": "7",
          "sequenceNo": "7",
          "recipeNo": "1",
          "DateTime": "2025-04-15 10:47:41",
          "fillingStopTime": "1903",
          "fillingRPM": "5",
          "milkQuantityInVat": "10692",
          "temperatureOfMilkInVat": "31.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2510",
          "coagulationStartTime": "2847",
          "coagulationStopTime": "4907",
          "cuttingStartTime": "4969",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5161",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5546",
          "cookingStopTime": "7656",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "10794",
          "emptyingStopTime": "13652",
          "residenceTimeInVat": "8891",
          "totalProcesstimeInVat": "13652",
          "coagulationDuration": "2060",
          "cookingDuration": "2110",
          "emptyingDuration": "2858",
          "vatpH": "6.45"
      },
      {
          "vatNo": "1",
          "sequenceNo": "8",
          "recipeNo": "1",
          "DateTime": "2025-04-15 11:28:40",
          "fillingStopTime": "1986",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "32.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "281",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2584",
          "coagulationStartTime": "2904",
          "coagulationStopTime": "5022",
          "cuttingStartTime": "5092",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5276",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5686",
          "cookingStopTime": "8922",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "10942",
          "emptyingStopTime": "12790",
          "residenceTimeInVat": "8956",
          "totalProcesstimeInVat": "12790",
          "coagulationDuration": "2118",
          "cookingDuration": "3236",
          "emptyingDuration": "1848",
          "vatpH": "6.51"
      },
      {
          "vatNo": "2",
          "sequenceNo": "9",
          "recipeNo": "1",
          "DateTime": "2025-04-15 12:02:01",
          "fillingStopTime": "2003",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "303",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2594",
          "coagulationStartTime": "2945",
          "coagulationStopTime": "5159",
          "cuttingStartTime": "5244",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5244",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5657",
          "cookingStopTime": "8344",
          "cookingRPM": "5",
          "cookingTemperature": "38.7",
          "emptyingStartTime": "10540",
          "emptyingStopTime": "12463",
          "residenceTimeInVat": "8537",
          "totalProcesstimeInVat": "12463",
          "coagulationDuration": "2214",
          "cookingDuration": "2687",
          "emptyingDuration": "1923",
          "vatpH": "6.46"
      },
      {
          "vatNo": "3",
          "sequenceNo": "10",
          "recipeNo": "1",
          "DateTime": "2025-04-15 12:35:24",
          "fillingStopTime": "2012",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "294",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2652",
          "coagulationStartTime": "3156",
          "coagulationStopTime": "5042",
          "cuttingStartTime": "5100",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5301",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5689",
          "cookingStopTime": "7771",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "10410",
          "emptyingStopTime": "15034",
          "residenceTimeInVat": "8398",
          "totalProcesstimeInVat": "15034",
          "coagulationDuration": "1886",
          "cookingDuration": "2082",
          "emptyingDuration": "4624",
          "vatpH": "6.45"
      },
      {
          "vatNo": "4",
          "sequenceNo": "11",
          "recipeNo": "1",
          "DateTime": "2025-04-15 13:15:52",
          "fillingStopTime": "4121",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "284",
          "rennetQuantity": "200",
          "rennetAdditionTime": "4707",
          "coagulationStartTime": "5054",
          "coagulationStopTime": "7091",
          "cuttingStartTime": "7178",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "7333",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "7734",
          "cookingStopTime": "9949",
          "cookingRPM": "5",
          "cookingTemperature": "38.8",
          "emptyingStartTime": "11845",
          "emptyingStopTime": "14267",
          "residenceTimeInVat": "7724",
          "totalProcesstimeInVat": "14267",
          "coagulationDuration": "2037",
          "cookingDuration": "2215",
          "emptyingDuration": "2422",
          "vatpH": "6.32"
      },
      {
          "vatNo": "6",
          "sequenceNo": "13",
          "recipeNo": "1",
          "DateTime": "2025-04-15 14:59:53",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "302",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2633",
          "coagulationStartTime": "2972",
          "coagulationStopTime": "5054",
          "cuttingStartTime": "5205",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5386",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5783",
          "cookingStopTime": "7915",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "9784",
          "emptyingStopTime": "12019",
          "residenceTimeInVat": "7782",
          "totalProcesstimeInVat": "12019",
          "coagulationDuration": "2082",
          "cookingDuration": "2132",
          "emptyingDuration": "2235",
          "vatpH": "6.18"
      },
      {
          "vatNo": "7",
          "sequenceNo": "14",
          "recipeNo": "1",
          "DateTime": "2025-04-15 15:33:15",
          "fillingStopTime": "2008",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "295",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2618",
          "coagulationStartTime": "2948",
          "coagulationStopTime": "5008",
          "cuttingStartTime": "5071",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5255",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5656",
          "cookingStopTime": "7782",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9668",
          "emptyingStopTime": "11702",
          "residenceTimeInVat": "7660",
          "totalProcesstimeInVat": "11702",
          "coagulationDuration": "2060",
          "cookingDuration": "2126",
          "emptyingDuration": "2034",
          "vatpH": "6.12"
      },
      {
          "vatNo": "1",
          "sequenceNo": "15",
          "recipeNo": "2",
          "DateTime": "2025-04-15 16:06:43",
          "fillingStopTime": "2468",
          "fillingRPM": "5",
          "milkQuantityInVat": "11486",
          "temperatureOfMilkInVat": "31.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "921",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2530",
          "coagulationStartTime": "2837",
          "coagulationStopTime": "5319",
          "cuttingStartTime": "5357",
          "cuttingRPM": "7",
          "intermittentCuttingStartTime": "5319",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5564",
          "cookingStopTime": "7660",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9376",
          "emptyingStopTime": "12134",
          "residenceTimeInVat": "6908",
          "totalProcesstimeInVat": "12134",
          "coagulationDuration": "2482",
          "cookingDuration": "2096",
          "emptyingDuration": "2758",
          "vatpH": "6.41"
      },
      {
          "vatNo": "2",
          "sequenceNo": "16",
          "recipeNo": "2",
          "DateTime": "2025-04-15 16:47:52",
          "fillingStopTime": "2011",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "914",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2060",
          "coagulationStartTime": "2850",
          "coagulationStopTime": "4464",
          "cuttingStartTime": "4527",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4709",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5070",
          "cookingStopTime": "7608",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9268",
          "emptyingStopTime": "11245",
          "residenceTimeInVat": "7257",
          "totalProcesstimeInVat": "11245",
          "coagulationDuration": "1614",
          "cookingDuration": "2538",
          "emptyingDuration": "1977",
          "vatpH": "6.21"
      },
      {
          "vatNo": "3",
          "sequenceNo": "17",
          "recipeNo": "2",
          "DateTime": "2025-04-15 17:24:30",
          "fillingStopTime": "2043",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "34.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "912",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2096",
          "coagulationStartTime": "2413",
          "coagulationStopTime": "4416",
          "cuttingStartTime": "4474",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4654",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5060",
          "cookingStopTime": "7184",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8842",
          "emptyingStopTime": "11168",
          "residenceTimeInVat": "6799",
          "totalProcesstimeInVat": "11168",
          "coagulationDuration": "2003",
          "cookingDuration": "2124",
          "emptyingDuration": "2326",
          "vatpH": "6.25"
      },
      {
          "vatNo": "4",
          "sequenceNo": "18",
          "recipeNo": "2",
          "DateTime": "2025-04-15 17:58:33",
          "fillingStopTime": "2004",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "909",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2050",
          "coagulationStartTime": "2364",
          "coagulationStopTime": "4450",
          "cuttingStartTime": "4512",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4698",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5086",
          "cookingStopTime": "7266",
          "cookingRPM": "5",
          "cookingTemperature": "38.6",
          "emptyingStartTime": "8971",
          "emptyingStopTime": "11451",
          "residenceTimeInVat": "6967",
          "totalProcesstimeInVat": "11451",
          "coagulationDuration": "2086",
          "cookingDuration": "2180",
          "emptyingDuration": "2480",
          "vatpH": "6.22"
      },
      {
          "vatNo": "5",
          "sequenceNo": "19",
          "recipeNo": "3",
          "DateTime": "2025-04-15 18:31:57",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "34.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1458",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2048",
          "coagulationStartTime": "2386",
          "coagulationStopTime": "4439",
          "cuttingStartTime": "4621",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4795",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5187",
          "cookingStopTime": "7482",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8870",
          "emptyingStopTime": "11185",
          "residenceTimeInVat": "6868",
          "totalProcesstimeInVat": "11185",
          "coagulationDuration": "2053",
          "cookingDuration": "2295",
          "emptyingDuration": "2315",
          "vatpH": "6.18"
      },
      {
          "vatNo": "6",
          "sequenceNo": "20",
          "recipeNo": "3",
          "DateTime": "2025-04-15 19:05:19",
          "fillingStopTime": "1993",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "34.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1456",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2060",
          "coagulationStartTime": "2394",
          "coagulationStopTime": "4481",
          "cuttingStartTime": "4605",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4784",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5172",
          "cookingStopTime": "7404",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8796",
          "emptyingStopTime": "11110",
          "residenceTimeInVat": "6803",
          "totalProcesstimeInVat": "11110",
          "coagulationDuration": "2087",
          "cookingDuration": "2232",
          "emptyingDuration": "2314",
          "vatpH": "6.21"
      },
      {
          "vatNo": "7",
          "sequenceNo": "21",
          "recipeNo": "3",
          "DateTime": "2025-04-15 19:38:32",
          "fillingStopTime": "2013",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "33.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1456",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2056",
          "coagulationStartTime": "2371",
          "coagulationStopTime": "4461",
          "cuttingStartTime": "4636",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4813",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5220",
          "cookingStopTime": "7392",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8811",
          "emptyingStopTime": "10797",
          "residenceTimeInVat": "6798",
          "totalProcesstimeInVat": "10797",
          "coagulationDuration": "2090",
          "cookingDuration": "2172",
          "emptyingDuration": "1986",
          "vatpH": "6.21"
      },
      {
          "vatNo": "1",
          "sequenceNo": "22",
          "recipeNo": "4",
          "DateTime": "2025-04-15 20:12:05",
          "fillingStopTime": "2005",
          "fillingRPM": "5",
          "milkQuantityInVat": "11615",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1817",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2048",
          "coagulationStartTime": "2362",
          "coagulationStopTime": "4609",
          "cuttingStartTime": "4684",
          "cuttingRPM": "7",
          "intermittentCuttingStartTime": "4684",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5085",
          "cookingStopTime": "7138",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8496",
          "emptyingStopTime": "10929",
          "residenceTimeInVat": "6491",
          "totalProcesstimeInVat": "10929",
          "coagulationDuration": "2247",
          "cookingDuration": "2053",
          "emptyingDuration": "2433",
          "vatpH": "6.29"
      },
      {
          "vatNo": "2",
          "sequenceNo": "23",
          "recipeNo": "4",
          "DateTime": "2025-04-15 20:45:30",
          "fillingStopTime": "1985",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1814",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2039",
          "coagulationStartTime": "2361",
          "coagulationStopTime": "4280",
          "cuttingStartTime": "4338",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4520",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4916",
          "cookingStopTime": "7470",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8651",
          "emptyingStopTime": "13596",
          "residenceTimeInVat": "6666",
          "totalProcesstimeInVat": "13596",
          "coagulationDuration": "1919",
          "cookingDuration": "2554",
          "emptyingDuration": "4945",
          "vatpH": "6.29"
      },
      {
          "vatNo": "2",
          "sequenceNo": "30",
          "recipeNo": "4",
          "DateTime": "2025-04-16 00:39:34",
          "fillingStopTime": "1965",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1780",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2047",
          "coagulationStartTime": "2353",
          "coagulationStopTime": "4415",
          "cuttingStartTime": "4478",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4658",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5060",
          "cookingStopTime": "7462",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8570",
          "emptyingStopTime": "10732",
          "residenceTimeInVat": "6605",
          "totalProcesstimeInVat": "10732",
          "coagulationDuration": "2062",
          "cookingDuration": "2402",
          "emptyingDuration": "2162",
          "vatpH": "6.33"
      },
      {
          "vatNo": "3",
          "sequenceNo": "31",
          "recipeNo": "4",
          "DateTime": "2025-04-16 01:13:41",
          "fillingStopTime": "1935",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1739",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1989",
          "coagulationStartTime": "2302",
          "coagulationStopTime": "4519",
          "cuttingStartTime": "4583",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4744",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5138",
          "cookingStopTime": "7198",
          "cookingRPM": "5",
          "cookingTemperature": "39.5",
          "emptyingStartTime": "8338",
          "emptyingStopTime": "10601",
          "residenceTimeInVat": "6403",
          "totalProcesstimeInVat": "10601",
          "coagulationDuration": "2217",
          "cookingDuration": "2060",
          "emptyingDuration": "2263",
          "vatpH": "6.34"
      },
      {
          "vatNo": "4",
          "sequenceNo": "32",
          "recipeNo": "4",
          "DateTime": "2025-04-16 01:46:50",
          "fillingStopTime": "1947",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1764",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2000",
          "coagulationStartTime": "2309",
          "coagulationStopTime": "4381",
          "cuttingStartTime": "4444",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4624",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5018",
          "cookingStopTime": "7295",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8403",
          "emptyingStopTime": "10384",
          "residenceTimeInVat": "6456",
          "totalProcesstimeInVat": "10384",
          "coagulationDuration": "2072",
          "cookingDuration": "2277",
          "emptyingDuration": "1981",
          "vatpH": "6.36"
      },
      {
          "vatNo": "5",
          "sequenceNo": "33",
          "recipeNo": "4",
          "DateTime": "2025-04-16 02:19:33",
          "fillingStopTime": "1967",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1801",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2019",
          "coagulationStartTime": "2327",
          "coagulationStopTime": "4399",
          "cuttingStartTime": "4462",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4641",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5035",
          "cookingStopTime": "7096",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8207",
          "emptyingStopTime": "10478",
          "residenceTimeInVat": "6240",
          "totalProcesstimeInVat": "10478",
          "coagulationDuration": "2072",
          "cookingDuration": "2061",
          "emptyingDuration": "2271",
          "vatpH": "6.36"
      },
      {
          "vatNo": "7",
          "sequenceNo": "35",
          "recipeNo": "4",
          "DateTime": "2025-04-16 03:28:54",
          "fillingStopTime": "2279",
          "fillingRPM": "5",
          "milkQuantityInVat": "10984",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1626",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2323",
          "coagulationStartTime": "2643",
          "coagulationStopTime": "4727",
          "cuttingStartTime": "4778",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4958",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5352",
          "cookingStopTime": "7453",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8558",
          "emptyingStopTime": "10421",
          "residenceTimeInVat": "6279",
          "totalProcesstimeInVat": "10421",
          "coagulationDuration": "2084",
          "cookingDuration": "2101",
          "emptyingDuration": "1863",
          "vatpH": "6.27"
      },
      {
          "vatNo": "1",
          "sequenceNo": "1",
          "recipeNo": "1",
          "DateTime": "2025-04-16 13:30:06",
          "fillingStopTime": "1995",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "314",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2629",
          "coagulationStartTime": "2985",
          "coagulationStopTime": "5070",
          "cuttingStartTime": "5132",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5312",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5706",
          "cookingStopTime": "7819",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9699",
          "emptyingStopTime": "12216",
          "residenceTimeInVat": "7704",
          "totalProcesstimeInVat": "12216",
          "coagulationDuration": "2085",
          "cookingDuration": "2113",
          "emptyingDuration": "2517",
          "vatpH": "6.49"
      },
      {
          "vatNo": "2",
          "sequenceNo": "2",
          "recipeNo": "1",
          "DateTime": "2025-04-16 14:03:22",
          "fillingStopTime": "2018",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "300",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2664",
          "coagulationStartTime": "2984",
          "coagulationStopTime": "5085",
          "cuttingStartTime": "5128",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5299",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5693",
          "cookingStopTime": "8079",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9947",
          "emptyingStopTime": "12618",
          "residenceTimeInVat": "7929",
          "totalProcesstimeInVat": "12618",
          "coagulationDuration": "2101",
          "cookingDuration": "2386",
          "emptyingDuration": "2671",
          "vatpH": "6.49"
      },
      {
          "vatNo": "3",
          "sequenceNo": "3",
          "recipeNo": "1",
          "DateTime": "2025-04-16 14:37:14",
          "fillingStopTime": "1992",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "291",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2631",
          "coagulationStartTime": "3053",
          "coagulationStopTime": "5042",
          "cuttingStartTime": "5104",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5284",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5678",
          "cookingStopTime": "7810",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "10271",
          "emptyingStopTime": "12190",
          "residenceTimeInVat": "8279",
          "totalProcesstimeInVat": "12190",
          "coagulationDuration": "1989",
          "cookingDuration": "2132",
          "emptyingDuration": "1919",
          "vatpH": "6.5"
      },
      {
          "vatNo": "4",
          "sequenceNo": "4",
          "recipeNo": "1",
          "DateTime": "2025-04-16 15:10:27",
          "fillingStopTime": "2003",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "301",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2653",
          "coagulationStartTime": "3094",
          "coagulationStopTime": "5161",
          "cuttingStartTime": "5224",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5405",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5814",
          "cookingStopTime": "7883",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9936",
          "emptyingStopTime": "11970",
          "residenceTimeInVat": "7933",
          "totalProcesstimeInVat": "11970",
          "coagulationDuration": "2067",
          "cookingDuration": "2069",
          "emptyingDuration": "2034",
          "vatpH": "6.5"
      },
      {
          "vatNo": "5",
          "sequenceNo": "5",
          "recipeNo": "1",
          "DateTime": "2025-04-16 15:44:05",
          "fillingStopTime": "1995",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2593",
          "coagulationStartTime": "2920",
          "coagulationStopTime": "5010",
          "cuttingStartTime": "5073",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5253",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5647",
          "cookingStopTime": "7746",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9658",
          "emptyingStopTime": "11813",
          "residenceTimeInVat": "7663",
          "totalProcesstimeInVat": "11813",
          "coagulationDuration": "2090",
          "cookingDuration": "2099",
          "emptyingDuration": "2155",
          "vatpH": "6.48"
      },
      {
          "vatNo": "6",
          "sequenceNo": "6",
          "recipeNo": "1",
          "DateTime": "2025-04-16 16:17:37",
          "fillingStopTime": "1986",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32",
          "cultureQuantity": "200",
          "cultureAdditionTime": "283",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2586",
          "coagulationStartTime": "2943",
          "coagulationStopTime": "5009",
          "cuttingStartTime": "5072",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5251",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5645",
          "cookingStopTime": "7884",
          "cookingRPM": "5",
          "cookingTemperature": "38.6",
          "emptyingStartTime": "9572",
          "emptyingStopTime": "12078",
          "residenceTimeInVat": "7586",
          "totalProcesstimeInVat": "12078",
          "coagulationDuration": "2066",
          "cookingDuration": "2239",
          "emptyingDuration": "2506",
          "vatpH": "6.46"
      },
      {
          "vatNo": "7",
          "sequenceNo": "7",
          "recipeNo": "1",
          "DateTime": "2025-04-16 16:51:00",
          "fillingStopTime": "1990",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "285",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2602",
          "coagulationStartTime": "2926",
          "coagulationStopTime": "5005",
          "cuttingStartTime": "5067",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5247",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5643",
          "cookingStopTime": "7717",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9538",
          "emptyingStopTime": "12155",
          "residenceTimeInVat": "7548",
          "totalProcesstimeInVat": "12155",
          "coagulationDuration": "2079",
          "cookingDuration": "2074",
          "emptyingDuration": "2617",
          "vatpH": "6.45"
      },
      {
          "vatNo": "1",
          "sequenceNo": "8",
          "recipeNo": "1",
          "DateTime": "2025-04-16 17:30:53",
          "fillingStopTime": "1998",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "301",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2598",
          "coagulationStartTime": "2919",
          "coagulationStopTime": "5004",
          "cuttingStartTime": "5066",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5246",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5640",
          "cookingStopTime": "7703",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9537",
          "emptyingStopTime": "12614",
          "residenceTimeInVat": "7539",
          "totalProcesstimeInVat": "12614",
          "coagulationDuration": "2085",
          "cookingDuration": "2063",
          "emptyingDuration": "3077",
          "vatpH": "6.48"
      },
      {
          "vatNo": "2",
          "sequenceNo": "9",
          "recipeNo": "1",
          "DateTime": "2025-04-16 18:06:51",
          "fillingStopTime": "2021",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "34.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "302",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2618",
          "coagulationStartTime": "2932",
          "coagulationStopTime": "4998",
          "cuttingStartTime": "5061",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5241",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5635",
          "cookingStopTime": "8533",
          "cookingRPM": "5",
          "cookingTemperature": "38.6",
          "emptyingStartTime": "10235",
          "emptyingStopTime": "12202",
          "residenceTimeInVat": "8214",
          "totalProcesstimeInVat": "12202",
          "coagulationDuration": "2066",
          "cookingDuration": "2898",
          "emptyingDuration": "1967",
          "vatpH": "6.47"
      },
      {
          "vatNo": "3",
          "sequenceNo": "10",
          "recipeNo": "1",
          "DateTime": "2025-04-16 18:40:33",
          "fillingStopTime": "2007",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "303",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2606",
          "coagulationStartTime": "2965",
          "coagulationStopTime": "5029",
          "cuttingStartTime": "5092",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5272",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5666",
          "cookingStopTime": "7726",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "10095",
          "emptyingStopTime": "11831",
          "residenceTimeInVat": "8088",
          "totalProcesstimeInVat": "11831",
          "coagulationDuration": "2064",
          "cookingDuration": "2060",
          "emptyingDuration": "1736",
          "vatpH": "6.48"
      },
      {
          "vatNo": "4",
          "sequenceNo": "11",
          "recipeNo": "1",
          "DateTime": "2025-04-16 19:14:01",
          "fillingStopTime": "1996",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "299",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2596",
          "coagulationStartTime": "2910",
          "coagulationStopTime": "4982",
          "cuttingStartTime": "5045",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5224",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5618",
          "cookingStopTime": "7788",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9627",
          "emptyingStopTime": "12261",
          "residenceTimeInVat": "7631",
          "totalProcesstimeInVat": "12261",
          "coagulationDuration": "2072",
          "cookingDuration": "2170",
          "emptyingDuration": "2634",
          "vatpH": "6.45"
      },
      {
          "vatNo": "5",
          "sequenceNo": "12",
          "recipeNo": "1",
          "DateTime": "2025-04-16 19:47:36",
          "fillingStopTime": "1986",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "285",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2581",
          "coagulationStartTime": "2907",
          "coagulationStopTime": "4992",
          "cuttingStartTime": "5055",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5234",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5629",
          "cookingStopTime": "7689",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "10049",
          "emptyingStopTime": "11953",
          "residenceTimeInVat": "8063",
          "totalProcesstimeInVat": "11953",
          "coagulationDuration": "2085",
          "cookingDuration": "2060",
          "emptyingDuration": "1904",
          "vatpH": "6.38"
      },
      {
          "vatNo": "7",
          "sequenceNo": "14",
          "recipeNo": "1",
          "DateTime": "2025-04-16 20:54:20",
          "fillingStopTime": "2068",
          "fillingRPM": "5",
          "milkQuantityInVat": "11263",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "291",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2593",
          "coagulationStartTime": "2926",
          "coagulationStopTime": "5010",
          "cuttingStartTime": "5073",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5253",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5686",
          "cookingStopTime": "7721",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9616",
          "emptyingStopTime": "11530",
          "residenceTimeInVat": "7548",
          "totalProcesstimeInVat": "11530",
          "coagulationDuration": "2084",
          "cookingDuration": "2035",
          "emptyingDuration": "1914",
          "vatpH": "6.38"
      },
      {
          "vatNo": "1",
          "sequenceNo": "15",
          "recipeNo": "2",
          "DateTime": "2025-04-16 21:34:28",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "897",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2043",
          "coagulationStartTime": "2363",
          "coagulationStopTime": "4442",
          "cuttingStartTime": "4504",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4684",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5078",
          "cookingStopTime": "7138",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8920",
          "emptyingStopTime": "10811",
          "residenceTimeInVat": "6929",
          "totalProcesstimeInVat": "10811",
          "coagulationDuration": "2079",
          "cookingDuration": "2060",
          "emptyingDuration": "1891",
          "vatpH": "6.37"
      },
      {
          "vatNo": "2",
          "sequenceNo": "16",
          "recipeNo": "2",
          "DateTime": "2025-04-16 22:07:57",
          "fillingStopTime": "2247",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "32.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "890",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2302",
          "coagulationStartTime": "2616",
          "coagulationStopTime": "4865",
          "cuttingStartTime": "4949",
          "cuttingRPM": "7",
          "intermittentCuttingStartTime": "4949",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5313",
          "cookingStopTime": "6969",
          "cookingRPM": "5",
          "cookingTemperature": "37.1",
          "emptyingStartTime": "8623",
          "emptyingStopTime": "10758",
          "residenceTimeInVat": "6376",
          "totalProcesstimeInVat": "10758",
          "coagulationDuration": "2249",
          "cookingDuration": "1656",
          "emptyingDuration": "2135",
          "vatpH": "6.41"
      },
      {
          "vatNo": "3",
          "sequenceNo": "17",
          "recipeNo": "2",
          "DateTime": "2025-04-16 22:45:55",
          "fillingStopTime": "1975",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "877",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2028",
          "coagulationStartTime": "2336",
          "coagulationStopTime": "4403",
          "cuttingStartTime": "4465",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4645",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5039",
          "cookingStopTime": "7100",
          "cookingRPM": "5",
          "cookingTemperature": "39.5",
          "emptyingStartTime": "8296",
          "emptyingStopTime": "11006",
          "residenceTimeInVat": "6321",
          "totalProcesstimeInVat": "11006",
          "coagulationDuration": "2067",
          "cookingDuration": "2061",
          "emptyingDuration": "2710",
          "vatpH": "6.36"
      },
      {
          "vatNo": "4",
          "sequenceNo": "18",
          "recipeNo": "2",
          "DateTime": "2025-04-16 23:18:51",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "904",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2056",
          "coagulationStartTime": "2376",
          "coagulationStopTime": "4452",
          "cuttingStartTime": "4505",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4685",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5079",
          "cookingStopTime": "7262",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8857",
          "emptyingStopTime": "11089",
          "residenceTimeInVat": "6855",
          "totalProcesstimeInVat": "11089",
          "coagulationDuration": "2076",
          "cookingDuration": "2183",
          "emptyingDuration": "2232",
          "vatpH": "6.35"
      },
      {
          "vatNo": "6",
          "sequenceNo": "20",
          "recipeNo": "2",
          "DateTime": "2025-04-17 00:28:58",
          "fillingStopTime": "1798",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "703",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1848",
          "coagulationStartTime": "2156",
          "coagulationStopTime": "4229",
          "cuttingStartTime": "4291",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4471",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4865",
          "cookingStopTime": "6934",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8523",
          "emptyingStopTime": "11636",
          "residenceTimeInVat": "6725",
          "totalProcesstimeInVat": "11636",
          "coagulationDuration": "2073",
          "cookingDuration": "2069",
          "emptyingDuration": "3113",
          "vatpH": "6.3"
      },
      {
          "vatNo": "7",
          "sequenceNo": "21",
          "recipeNo": "2",
          "DateTime": "2025-04-17 00:59:10",
          "fillingStopTime": "1993",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "34.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "896",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2048",
          "coagulationStartTime": "2362",
          "coagulationStopTime": "4441",
          "cuttingStartTime": "4503",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4683",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5085",
          "cookingStopTime": "7321",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9759",
          "emptyingStopTime": "11267",
          "residenceTimeInVat": "7766",
          "totalProcesstimeInVat": "11267",
          "coagulationDuration": "2079",
          "cookingDuration": "2236",
          "emptyingDuration": "1508",
          "vatpH": "6.31"
      },
      {
          "vatNo": "1",
          "sequenceNo": "22",
          "recipeNo": "3",
          "DateTime": "2025-04-17 01:32:37",
          "fillingStopTime": "1992",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1444",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2044",
          "coagulationStartTime": "2358",
          "coagulationStopTime": "4442",
          "cuttingStartTime": "4505",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4685",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5079",
          "cookingStopTime": "7260",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9289",
          "emptyingStopTime": "11400",
          "residenceTimeInVat": "7297",
          "totalProcesstimeInVat": "11400",
          "coagulationDuration": "2084",
          "cookingDuration": "2181",
          "emptyingDuration": "2111",
          "vatpH": "6.37"
      },
      {
          "vatNo": "2",
          "sequenceNo": "23",
          "recipeNo": "3",
          "DateTime": "2025-04-17 02:06:03",
          "fillingStopTime": "1993",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1439",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2045",
          "coagulationStartTime": "2359",
          "coagulationStopTime": "4426",
          "cuttingStartTime": "4488",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4668",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5063",
          "cookingStopTime": "7392",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9446",
          "emptyingStopTime": "10968",
          "residenceTimeInVat": "7453",
          "totalProcesstimeInVat": "10968",
          "coagulationDuration": "2067",
          "cookingDuration": "2329",
          "emptyingDuration": "1522",
          "vatpH": "6.4"
      },
      {
          "vatNo": "3",
          "sequenceNo": "24",
          "recipeNo": "4",
          "DateTime": "2025-04-17 02:42:25",
          "fillingStopTime": "1976",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1788",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2031",
          "coagulationStartTime": "2345",
          "coagulationStopTime": "4412",
          "cuttingStartTime": "4475",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4654",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5048",
          "cookingStopTime": "7123",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8845",
          "emptyingStopTime": "10571",
          "residenceTimeInVat": "6869",
          "totalProcesstimeInVat": "10571",
          "coagulationDuration": "2067",
          "cookingDuration": "2075",
          "emptyingDuration": "1726",
          "vatpH": "6.4"
      },
      {
          "vatNo": "4",
          "sequenceNo": "25",
          "recipeNo": "4",
          "DateTime": "2025-04-17 03:15:22",
          "fillingStopTime": "1997",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1816",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2046",
          "coagulationStartTime": "2361",
          "coagulationStopTime": "4458",
          "cuttingStartTime": "4489",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4669",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5068",
          "cookingStopTime": "7234",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8617",
          "emptyingStopTime": "11084",
          "residenceTimeInVat": "6620",
          "totalProcesstimeInVat": "11084",
          "coagulationDuration": "2097",
          "cookingDuration": "2166",
          "emptyingDuration": "2467",
          "vatpH": "6.39"
      },
      {
          "vatNo": "5",
          "sequenceNo": "26",
          "recipeNo": "4",
          "DateTime": "2025-04-17 03:48:52",
          "fillingStopTime": "1980",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "34.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1808",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2033",
          "coagulationStartTime": "2393",
          "coagulationStopTime": "4437",
          "cuttingStartTime": "4500",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4680",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5074",
          "cookingStopTime": "7246",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8833",
          "emptyingStopTime": "11352",
          "residenceTimeInVat": "6853",
          "totalProcesstimeInVat": "11352",
          "coagulationDuration": "2044",
          "cookingDuration": "2172",
          "emptyingDuration": "2519",
          "vatpH": "6.4"
      },
      {
          "vatNo": "6",
          "sequenceNo": "27",
          "recipeNo": "4",
          "DateTime": "2025-04-17 04:22:08",
          "fillingStopTime": "1982",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1803",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2033",
          "coagulationStartTime": "2347",
          "coagulationStopTime": "4407",
          "cuttingStartTime": "4470",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4650",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5044",
          "cookingStopTime": "7145",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8794",
          "emptyingStopTime": "10829",
          "residenceTimeInVat": "6812",
          "totalProcesstimeInVat": "10829",
          "coagulationDuration": "2060",
          "cookingDuration": "2101",
          "emptyingDuration": "2035",
          "vatpH": "6.33"
      },
      {
          "vatNo": "7",
          "sequenceNo": "28",
          "recipeNo": "4",
          "DateTime": "2025-04-17 04:55:24",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "34.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1807",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2071",
          "coagulationStartTime": "2357",
          "coagulationStopTime": "4442",
          "cuttingStartTime": "4635",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4812",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5206",
          "cookingStopTime": "7477",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8580",
          "emptyingStopTime": "10656",
          "residenceTimeInVat": "6578",
          "totalProcesstimeInVat": "10656",
          "coagulationDuration": "2085",
          "cookingDuration": "2271",
          "emptyingDuration": "2076",
          "vatpH": "6.31"
      },
      {
          "vatNo": "1",
          "sequenceNo": "29",
          "recipeNo": "4",
          "DateTime": "2025-04-17 05:28:46",
          "fillingStopTime": "1985",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1801",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2037",
          "coagulationStartTime": "2369",
          "coagulationStopTime": "4454",
          "cuttingStartTime": "4652",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4830",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5223",
          "cookingStopTime": "7284",
          "cookingRPM": "5",
          "cookingTemperature": "39.3",
          "emptyingStartTime": "8392",
          "emptyingStopTime": "10802",
          "residenceTimeInVat": "6407",
          "totalProcesstimeInVat": "10802",
          "coagulationDuration": "2085",
          "cookingDuration": "2061",
          "emptyingDuration": "2410",
          "vatpH": "6.34"
      },
      {
          "vatNo": "2",
          "sequenceNo": "30",
          "recipeNo": "4",
          "DateTime": "2025-04-17 06:02:05",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1803",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2046",
          "coagulationStartTime": "2427",
          "coagulationStopTime": "4517",
          "cuttingStartTime": "4580",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4760",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5154",
          "cookingStopTime": "7340",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8488",
          "emptyingStopTime": "10500",
          "residenceTimeInVat": "6497",
          "totalProcesstimeInVat": "10500",
          "coagulationDuration": "2090",
          "cookingDuration": "2186",
          "emptyingDuration": "2012",
          "vatpH": "6.38"
      },
      {
          "vatNo": "3",
          "sequenceNo": "31",
          "recipeNo": "4",
          "DateTime": "2025-04-17 06:35:29",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1803",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2052",
          "coagulationStartTime": "2360",
          "coagulationStopTime": "4453",
          "cuttingStartTime": "4507",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4687",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5081",
          "cookingStopTime": "7145",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8250",
          "emptyingStopTime": "10632",
          "residenceTimeInVat": "6248",
          "totalProcesstimeInVat": "10632",
          "coagulationDuration": "2093",
          "cookingDuration": "2064",
          "emptyingDuration": "2382",
          "vatpH": "6.41"
      },
      {
          "vatNo": "6",
          "sequenceNo": "34",
          "recipeNo": "4",
          "DateTime": "2025-04-17 08:15:35",
          "fillingStopTime": "1984",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1819",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2036",
          "coagulationStartTime": "2417",
          "coagulationStopTime": "4503",
          "cuttingStartTime": "4562",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4744",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5134",
          "cookingStopTime": "7240",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8400",
          "emptyingStopTime": "10517",
          "residenceTimeInVat": "6416",
          "totalProcesstimeInVat": "10517",
          "coagulationDuration": "2086",
          "cookingDuration": "2106",
          "emptyingDuration": "2117",
          "vatpH": "6.35"
      },
      {
          "vatNo": "7",
          "sequenceNo": "35",
          "recipeNo": "4",
          "DateTime": "2025-04-17 08:48:53",
          "fillingStopTime": "1997",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1811",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2054",
          "coagulationStartTime": "2409",
          "coagulationStopTime": "4495",
          "cuttingStartTime": "4583",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4757",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5158",
          "cookingStopTime": "7212",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8334",
          "emptyingStopTime": "10588",
          "residenceTimeInVat": "6337",
          "totalProcesstimeInVat": "10588",
          "coagulationDuration": "2086",
          "cookingDuration": "2054",
          "emptyingDuration": "2254",
          "vatpH": "6.36"
      },
      {
          "vatNo": "1",
          "sequenceNo": "36",
          "recipeNo": "4",
          "DateTime": "2025-04-17 09:22:10",
          "fillingStopTime": "1994",
          "fillingRPM": "5",
          "milkQuantityInVat": "10936",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1817",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2056",
          "coagulationStartTime": "2370",
          "coagulationStopTime": "4450",
          "cuttingStartTime": "4513",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4690",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5089",
          "cookingStopTime": "7204",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8373",
          "emptyingStopTime": "10138",
          "residenceTimeInVat": "6379",
          "totalProcesstimeInVat": "10138",
          "coagulationDuration": "2080",
          "cookingDuration": "2115",
          "emptyingDuration": "1765",
          "vatpH": "6.36"
      },
      {
          "vatNo": "1",
          "sequenceNo": "1",
          "recipeNo": "1",
          "DateTime": "2025-04-17 16:54:37",
          "fillingStopTime": "2033",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "31.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "308",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2657",
          "coagulationStartTime": "2973",
          "coagulationStopTime": "5051",
          "cuttingStartTime": "5111",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5284",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5687",
          "cookingStopTime": "7744",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9644",
          "emptyingStopTime": "12045",
          "residenceTimeInVat": "7611",
          "totalProcesstimeInVat": "12045",
          "coagulationDuration": "2078",
          "cookingDuration": "2057",
          "emptyingDuration": "2401",
          "vatpH": "6.46"
      },
      {
          "vatNo": "2",
          "sequenceNo": "2",
          "recipeNo": "1",
          "DateTime": "2025-04-17 17:28:30",
          "fillingStopTime": "1995",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "286",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2595",
          "coagulationStartTime": "2906",
          "coagulationStopTime": "4978",
          "cuttingStartTime": "5046",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5231",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5614",
          "cookingStopTime": "7847",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "9743",
          "emptyingStopTime": "11889",
          "residenceTimeInVat": "7748",
          "totalProcesstimeInVat": "11889",
          "coagulationDuration": "2072",
          "cookingDuration": "2233",
          "emptyingDuration": "2146",
          "vatpH": "6.46"
      },
      {
          "vatNo": "3",
          "sequenceNo": "3",
          "recipeNo": "1",
          "DateTime": "2025-04-17 18:01:45",
          "fillingStopTime": "2019",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2609",
          "coagulationStartTime": "2944",
          "coagulationStopTime": "4966",
          "cuttingStartTime": "5014",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5214",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5601",
          "cookingStopTime": "7762",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9657",
          "emptyingStopTime": "11821",
          "residenceTimeInVat": "7638",
          "totalProcesstimeInVat": "11821",
          "coagulationDuration": "2022",
          "cookingDuration": "2161",
          "emptyingDuration": "2164",
          "vatpH": "6.42"
      },
      {
          "vatNo": "4",
          "sequenceNo": "4",
          "recipeNo": "1",
          "DateTime": "2025-04-17 18:35:24",
          "fillingStopTime": "2009",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "31.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "295",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2598",
          "coagulationStartTime": "2931",
          "coagulationStopTime": "5026",
          "cuttingStartTime": "5091",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5268",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5662",
          "cookingStopTime": "7718",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "9595",
          "emptyingStopTime": "11807",
          "residenceTimeInVat": "7586",
          "totalProcesstimeInVat": "11807",
          "coagulationDuration": "2095",
          "cookingDuration": "2056",
          "emptyingDuration": "2212",
          "vatpH": "6.47"
      },
      {
          "vatNo": "7",
          "sequenceNo": "7",
          "recipeNo": "1",
          "DateTime": "2025-04-17 20:15:39",
          "fillingStopTime": "2012",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "33.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "305",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2601",
          "coagulationStartTime": "2938",
          "coagulationStopTime": "5003",
          "cuttingStartTime": "5062",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5247",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5649",
          "cookingStopTime": "7699",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9581",
          "emptyingStopTime": "11891",
          "residenceTimeInVat": "7569",
          "totalProcesstimeInVat": "11891",
          "coagulationDuration": "2065",
          "cookingDuration": "2050",
          "emptyingDuration": "2310",
          "vatpH": "6.46"
      },
      {
          "vatNo": "1",
          "sequenceNo": "8",
          "recipeNo": "1",
          "DateTime": "2025-04-17 20:50:11",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "34",
          "cultureQuantity": "200",
          "cultureAdditionTime": "287",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2595",
          "coagulationStartTime": "2916",
          "coagulationStopTime": "4983",
          "cuttingStartTime": "5045",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5236",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5627",
          "cookingStopTime": "7685",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9615",
          "emptyingStopTime": "11693",
          "residenceTimeInVat": "7624",
          "totalProcesstimeInVat": "11693",
          "coagulationDuration": "2067",
          "cookingDuration": "2058",
          "emptyingDuration": "2078",
          "vatpH": "6.46"
      },
      {
          "vatNo": "2",
          "sequenceNo": "9",
          "recipeNo": "1",
          "DateTime": "2025-04-17 21:23:36",
          "fillingStopTime": "1990",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "308",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2593",
          "coagulationStartTime": "2912",
          "coagulationStopTime": "4968",
          "cuttingStartTime": "5045",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5210",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5604",
          "cookingStopTime": "8254",
          "cookingRPM": "5",
          "cookingTemperature": "38.7",
          "emptyingStartTime": "9441",
          "emptyingStopTime": "11915",
          "residenceTimeInVat": "7451",
          "totalProcesstimeInVat": "11915",
          "coagulationDuration": "2056",
          "cookingDuration": "2650",
          "emptyingDuration": "2474",
          "vatpH": "6.45"
      },
      {
          "vatNo": "3",
          "sequenceNo": "10",
          "recipeNo": "1",
          "DateTime": "2025-04-17 21:57:00",
          "fillingStopTime": "2056",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "34.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "301",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2653",
          "coagulationStartTime": "2967",
          "coagulationStopTime": "5058",
          "cuttingStartTime": "5122",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5300",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5688",
          "cookingStopTime": "7798",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9705",
          "emptyingStopTime": "11937",
          "residenceTimeInVat": "7649",
          "totalProcesstimeInVat": "11937",
          "coagulationDuration": "2091",
          "cookingDuration": "2110",
          "emptyingDuration": "2232",
          "vatpH": "6.49"
      },
      {
          "vatNo": "4",
          "sequenceNo": "11",
          "recipeNo": "1",
          "DateTime": "2025-04-17 22:31:16",
          "fillingStopTime": "1992",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "34.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "307",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2599",
          "coagulationStartTime": "2924",
          "coagulationStopTime": "5008",
          "cuttingStartTime": "5046",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5226",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5628",
          "cookingStopTime": "7824",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9656",
          "emptyingStopTime": "11855",
          "residenceTimeInVat": "7664",
          "totalProcesstimeInVat": "11855",
          "coagulationDuration": "2084",
          "cookingDuration": "2196",
          "emptyingDuration": "2199",
          "vatpH": "6.45"
      },
      {
          "vatNo": "6",
          "sequenceNo": "13",
          "recipeNo": "1",
          "DateTime": "2025-04-17 23:38:22",
          "fillingStopTime": "1986",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "292",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2590",
          "coagulationStartTime": "2909",
          "coagulationStopTime": "4991",
          "cuttingStartTime": "5062",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5234",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5630",
          "cookingStopTime": "7747",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "10000",
          "emptyingStopTime": "11913",
          "residenceTimeInVat": "8014",
          "totalProcesstimeInVat": "11913",
          "coagulationDuration": "2082",
          "cookingDuration": "2117",
          "emptyingDuration": "1913",
          "vatpH": "6.45"
      },
      {
          "vatNo": "7",
          "sequenceNo": "14",
          "recipeNo": "1",
          "DateTime": "2025-04-18 00:11:57",
          "fillingStopTime": "1977",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "33.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "277",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2574",
          "coagulationStartTime": "2907",
          "coagulationStopTime": "4979",
          "cuttingStartTime": "5042",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5233",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5615",
          "cookingStopTime": "7750",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9695",
          "emptyingStopTime": "11808",
          "residenceTimeInVat": "7718",
          "totalProcesstimeInVat": "11808",
          "coagulationDuration": "2072",
          "cookingDuration": "2135",
          "emptyingDuration": "2113",
          "vatpH": "6.41"
      },
      {
          "vatNo": "1",
          "sequenceNo": "15",
          "recipeNo": "2",
          "DateTime": "2025-04-18 00:45:08",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "898",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2043",
          "coagulationStartTime": "2357",
          "coagulationStopTime": "4443",
          "cuttingStartTime": "4499",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4679",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5073",
          "cookingStopTime": "7149",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9410",
          "emptyingStopTime": "11336",
          "residenceTimeInVat": "7419",
          "totalProcesstimeInVat": "11336",
          "coagulationDuration": "2086",
          "cookingDuration": "2076",
          "emptyingDuration": "1926",
          "vatpH": "6.47"
      },
      {
          "vatNo": "2",
          "sequenceNo": "16",
          "recipeNo": "2",
          "DateTime": "2025-04-18 01:18:33",
          "fillingStopTime": "1994",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "895",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2047",
          "coagulationStartTime": "2362",
          "coagulationStopTime": "4440",
          "cuttingStartTime": "4507",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4683",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5046",
          "cookingStopTime": "7306",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9117",
          "emptyingStopTime": "11035",
          "residenceTimeInVat": "7123",
          "totalProcesstimeInVat": "11035",
          "coagulationDuration": "2078",
          "cookingDuration": "2260",
          "emptyingDuration": "1918",
          "vatpH": "6.42"
      },
      {
          "vatNo": "4",
          "sequenceNo": "18",
          "recipeNo": "2",
          "DateTime": "2025-04-18 02:28:43",
          "fillingStopTime": "1798",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "707",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1852",
          "coagulationStartTime": "2166",
          "coagulationStopTime": "4257",
          "cuttingStartTime": "4327",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4505",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4907",
          "cookingStopTime": "7204",
          "cookingRPM": "5",
          "cookingTemperature": "38.7",
          "emptyingStartTime": "8667",
          "emptyingStopTime": "10929",
          "residenceTimeInVat": "6869",
          "totalProcesstimeInVat": "10929",
          "coagulationDuration": "2091",
          "cookingDuration": "2297",
          "emptyingDuration": "2262",
          "vatpH": "6.38"
      },
      {
          "vatNo": "5",
          "sequenceNo": "19",
          "recipeNo": "2",
          "DateTime": "2025-04-18 03:02:12",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "33.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "897",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2042",
          "coagulationStartTime": "2381",
          "coagulationStopTime": "4447",
          "cuttingStartTime": "4510",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4690",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5195",
          "cookingStopTime": "7145",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8738",
          "emptyingStopTime": "10982",
          "residenceTimeInVat": "6747",
          "totalProcesstimeInVat": "10982",
          "coagulationDuration": "2066",
          "cookingDuration": "1950",
          "emptyingDuration": "2244",
          "vatpH": "6.32"
      },
      {
          "vatNo": "6",
          "sequenceNo": "20",
          "recipeNo": "2",
          "DateTime": "2025-04-18 03:35:37",
          "fillingStopTime": "1993",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "897",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2067",
          "coagulationStartTime": "2362",
          "coagulationStopTime": "4428",
          "cuttingStartTime": "4491",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4671",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5065",
          "cookingStopTime": "7172",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8784",
          "emptyingStopTime": "11238",
          "residenceTimeInVat": "6791",
          "totalProcesstimeInVat": "11238",
          "coagulationDuration": "2066",
          "cookingDuration": "2107",
          "emptyingDuration": "2454",
          "vatpH": "6.34"
      },
      {
          "vatNo": "7",
          "sequenceNo": "21",
          "recipeNo": "2",
          "DateTime": "2025-04-18 04:09:36",
          "fillingStopTime": "2063",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "35.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "908",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2114",
          "coagulationStartTime": "2434",
          "coagulationStopTime": "4507",
          "cuttingStartTime": "4571",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4749",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5143",
          "cookingStopTime": "7452",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9033",
          "emptyingStopTime": "10934",
          "residenceTimeInVat": "6970",
          "totalProcesstimeInVat": "10934",
          "coagulationDuration": "2073",
          "cookingDuration": "2309",
          "emptyingDuration": "1901",
          "vatpH": "6.3"
      },
      {
          "vatNo": "1",
          "sequenceNo": "22",
          "recipeNo": "3",
          "DateTime": "2025-04-18 04:44:14",
          "fillingStopTime": "1987",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1441",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2041",
          "coagulationStartTime": "2356",
          "coagulationStopTime": "4434",
          "cuttingStartTime": "4497",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4676",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5070",
          "cookingStopTime": "7137",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "8666",
          "emptyingStopTime": "10944",
          "residenceTimeInVat": "6679",
          "totalProcesstimeInVat": "10944",
          "coagulationDuration": "2078",
          "cookingDuration": "2067",
          "emptyingDuration": "2278",
          "vatpH": "6.4"
      },
      {
          "vatNo": "2",
          "sequenceNo": "23",
          "recipeNo": "3",
          "DateTime": "2025-04-18 05:17:37",
          "fillingStopTime": "1987",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1441",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2041",
          "coagulationStartTime": "2356",
          "coagulationStopTime": "4422",
          "cuttingStartTime": "4485",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4664",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5067",
          "cookingStopTime": "7348",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8717",
          "emptyingStopTime": "10698",
          "residenceTimeInVat": "6730",
          "totalProcesstimeInVat": "10698",
          "coagulationDuration": "2066",
          "cookingDuration": "2281",
          "emptyingDuration": "1981",
          "vatpH": "6.39"
      },
      {
          "vatNo": "3",
          "sequenceNo": "24",
          "recipeNo": "3",
          "DateTime": "2025-04-18 05:51:00",
          "fillingStopTime": "1993",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1441",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2047",
          "coagulationStartTime": "2361",
          "coagulationStopTime": "4440",
          "cuttingStartTime": "4502",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4682",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5076",
          "cookingStopTime": "7138",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8493",
          "emptyingStopTime": "10814",
          "residenceTimeInVat": "6500",
          "totalProcesstimeInVat": "10814",
          "coagulationDuration": "2079",
          "cookingDuration": "2062",
          "emptyingDuration": "2321",
          "vatpH": "6.4"
      },
      {
          "vatNo": "4",
          "sequenceNo": "25",
          "recipeNo": "3",
          "DateTime": "2025-04-18 06:24:41",
          "fillingStopTime": "1974",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1426",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2026",
          "coagulationStartTime": "2346",
          "coagulationStopTime": "4425",
          "cuttingStartTime": "4487",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4667",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5061",
          "cookingStopTime": "7219",
          "cookingRPM": "5",
          "cookingTemperature": "38.8",
          "emptyingStartTime": "8602",
          "emptyingStopTime": "10776",
          "residenceTimeInVat": "6628",
          "totalProcesstimeInVat": "10776",
          "coagulationDuration": "2079",
          "cookingDuration": "2158",
          "emptyingDuration": "2174",
          "vatpH": "6.38"
      },
      {
          "vatNo": "5",
          "sequenceNo": "26",
          "recipeNo": "3",
          "DateTime": "2025-04-18 06:57:50",
          "fillingStopTime": "1988",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "33.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1437",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2043",
          "coagulationStartTime": "2357",
          "coagulationStopTime": "4447",
          "cuttingStartTime": "4499",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4685",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5081",
          "cookingStopTime": "7146",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "8494",
          "emptyingStopTime": "10879",
          "residenceTimeInVat": "6506",
          "totalProcesstimeInVat": "10879",
          "coagulationDuration": "2090",
          "cookingDuration": "2065",
          "emptyingDuration": "2385",
          "vatpH": "6.43"
      },
      {
          "vatNo": "6",
          "sequenceNo": "27",
          "recipeNo": "3",
          "DateTime": "2025-04-18 07:31:13",
          "fillingStopTime": "1987",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1437",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2048",
          "coagulationStartTime": "2369",
          "coagulationStopTime": "4435",
          "cuttingStartTime": "4680",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4848",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5244",
          "cookingStopTime": "7340",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8680",
          "emptyingStopTime": "11039",
          "residenceTimeInVat": "6693",
          "totalProcesstimeInVat": "11039",
          "coagulationDuration": "2066",
          "cookingDuration": "2096",
          "emptyingDuration": "2359",
          "vatpH": "6.4"
      },
      {
          "vatNo": "7",
          "sequenceNo": "28",
          "recipeNo": "3",
          "DateTime": "2025-04-18 08:04:33",
          "fillingStopTime": "2007",
          "fillingRPM": "5",
          "milkQuantityInVat": "11263",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1452",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2053",
          "coagulationStartTime": "2403",
          "coagulationStopTime": "4491",
          "cuttingStartTime": "4556",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4740",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5124",
          "cookingStopTime": "7193",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8636",
          "emptyingStopTime": "10716",
          "residenceTimeInVat": "6629",
          "totalProcesstimeInVat": "10716",
          "coagulationDuration": "2088",
          "cookingDuration": "2069",
          "emptyingDuration": "2080",
          "vatpH": "6.4"
      },
      {
          "vatNo": "1",
          "sequenceNo": "29",
          "recipeNo": "4",
          "DateTime": "2025-04-18 08:38:00",
          "fillingStopTime": "1977",
          "fillingRPM": "5",
          "milkQuantityInVat": "11442",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1821",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2027",
          "coagulationStartTime": "2374",
          "coagulationStopTime": "4461",
          "cuttingStartTime": "4526",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4712",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5098",
          "cookingStopTime": "7175",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8492",
          "emptyingStopTime": "11605",
          "residenceTimeInVat": "6515",
          "totalProcesstimeInVat": "11605",
          "coagulationDuration": "2087",
          "cookingDuration": "2077",
          "emptyingDuration": "3113",
          "vatpH": "6.44"
      },
      {
          "vatNo": "2",
          "sequenceNo": "30",
          "recipeNo": "4",
          "DateTime": "2025-04-18 09:10:57",
          "fillingStopTime": "2583",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1825",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2628",
          "coagulationStartTime": "2977",
          "coagulationStopTime": "5055",
          "cuttingStartTime": "5114",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5302",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5687",
          "cookingStopTime": "8263",
          "cookingRPM": "5",
          "cookingTemperature": "38.7",
          "emptyingStartTime": "9399",
          "emptyingStopTime": "11317",
          "residenceTimeInVat": "6816",
          "totalProcesstimeInVat": "11317",
          "coagulationDuration": "2078",
          "cookingDuration": "2576",
          "emptyingDuration": "1918",
          "vatpH": "6.35"
      },
      {
          "vatNo": "3",
          "sequenceNo": "31",
          "recipeNo": "4",
          "DateTime": "2025-04-18 09:54:00",
          "fillingStopTime": "2069",
          "fillingRPM": "4",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1812",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2069",
          "coagulationStartTime": "2472",
          "coagulationStopTime": "4464",
          "cuttingStartTime": "4534",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4718",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5107",
          "cookingStopTime": "7165",
          "cookingRPM": "5",
          "cookingTemperature": "39.5",
          "emptyingStartTime": "8532",
          "emptyingStopTime": "10416",
          "residenceTimeInVat": "6463",
          "totalProcesstimeInVat": "10416",
          "coagulationDuration": "1992",
          "cookingDuration": "2058",
          "emptyingDuration": "1884",
          "vatpH": "6.42"
      },
      {
          "vatNo": "4",
          "sequenceNo": "32",
          "recipeNo": "4",
          "DateTime": "2025-04-18 10:28:29",
          "fillingStopTime": "1787",
          "fillingRPM": "5",
          "milkQuantityInVat": "10233",
          "temperatureOfMilkInVat": "32.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1754",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1830",
          "coagulationStartTime": "2158",
          "coagulationStopTime": "4229",
          "cuttingStartTime": "4290",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4469",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4877",
          "cookingStopTime": "6961",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8172",
          "emptyingStopTime": "10203",
          "residenceTimeInVat": "6385",
          "totalProcesstimeInVat": "10203",
          "coagulationDuration": "2071",
          "cookingDuration": "2084",
          "emptyingDuration": "2031",
          "vatpH": "6.39"
      },
      {
          "vatNo": "5",
          "sequenceNo": "33",
          "recipeNo": "4",
          "DateTime": "2025-04-18 10:58:16",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1824",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2048",
          "coagulationStartTime": "2398",
          "coagulationStopTime": "4476",
          "cuttingStartTime": "4531",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4707",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5101",
          "cookingStopTime": "7172",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8396",
          "emptyingStopTime": "11987",
          "residenceTimeInVat": "6394",
          "totalProcesstimeInVat": "11987",
          "coagulationDuration": "2078",
          "cookingDuration": "2071",
          "emptyingDuration": "3591",
          "vatpH": "6.41"
      },
      {
          "vatNo": "6",
          "sequenceNo": "34",
          "recipeNo": "4",
          "DateTime": "2025-04-18 11:31:38",
          "fillingStopTime": "1831",
          "fillingRPM": "5",
          "milkQuantityInVat": "10233",
          "temperatureOfMilkInVat": "32.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1813",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1899",
          "coagulationStartTime": "2251",
          "coagulationStopTime": "4334",
          "cuttingStartTime": "4397",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4573",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4971",
          "cookingStopTime": "7065",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9008",
          "emptyingStopTime": "11220",
          "residenceTimeInVat": "7177",
          "totalProcesstimeInVat": "11220",
          "coagulationDuration": "2083",
          "cookingDuration": "2094",
          "emptyingDuration": "2212",
          "vatpH": "6.43"
      },
      {
          "vatNo": "7",
          "sequenceNo": "35",
          "recipeNo": "4",
          "DateTime": "2025-04-18 12:02:09",
          "fillingStopTime": "1840",
          "fillingRPM": "3",
          "milkQuantityInVat": "10233",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1816",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1914",
          "coagulationStartTime": "2377",
          "coagulationStopTime": "4466",
          "cuttingStartTime": "4529",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4716",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5187",
          "cookingStopTime": "7161",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9418",
          "emptyingStopTime": "11774",
          "residenceTimeInVat": "7578",
          "totalProcesstimeInVat": "11774",
          "coagulationDuration": "2089",
          "cookingDuration": "1974",
          "emptyingDuration": "2356",
          "vatpH": "6.35"
      },
      {
          "vatNo": "1",
          "sequenceNo": "1",
          "recipeNo": "1",
          "DateTime": "2025-04-18 19:54:44",
          "fillingStopTime": "2040",
          "fillingRPM": "5",
          "milkQuantityInVat": "11615",
          "temperatureOfMilkInVat": "31.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "277",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2608",
          "coagulationStartTime": "2921",
          "coagulationStopTime": "5006",
          "cuttingStartTime": "5060",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5239",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5639",
          "cookingStopTime": "7708",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9592",
          "emptyingStopTime": "11905",
          "residenceTimeInVat": "7552",
          "totalProcesstimeInVat": "11905",
          "coagulationDuration": "2085",
          "cookingDuration": "2069",
          "emptyingDuration": "2313",
          "vatpH": "6.41"
      },
      {
          "vatNo": "2",
          "sequenceNo": "2",
          "recipeNo": "1",
          "DateTime": "2025-04-18 20:28:44",
          "fillingStopTime": "1914",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "31.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "213",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2510",
          "coagulationStartTime": "2833",
          "coagulationStopTime": "4921",
          "cuttingStartTime": "4978",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5167",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5555",
          "cookingStopTime": "7734",
          "cookingRPM": "5",
          "cookingTemperature": "38.7",
          "emptyingStartTime": "9613",
          "emptyingStopTime": "11886",
          "residenceTimeInVat": "7699",
          "totalProcesstimeInVat": "11886",
          "coagulationDuration": "2088",
          "cookingDuration": "2179",
          "emptyingDuration": "2273",
          "vatpH": "6.43"
      },
      {
          "vatNo": "3",
          "sequenceNo": "3",
          "recipeNo": "1",
          "DateTime": "2025-04-18 21:00:39",
          "fillingStopTime": "2030",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "31.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "303",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2615",
          "coagulationStartTime": "2939",
          "coagulationStopTime": "5020",
          "cuttingStartTime": "5094",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5287",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5656",
          "cookingStopTime": "7897",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9763",
          "emptyingStopTime": "12009",
          "residenceTimeInVat": "7733",
          "totalProcesstimeInVat": "12009",
          "coagulationDuration": "2081",
          "cookingDuration": "2241",
          "emptyingDuration": "2246",
          "vatpH": "6.42"
      },
      {
          "vatNo": "4",
          "sequenceNo": "4",
          "recipeNo": "1",
          "DateTime": "2025-04-18 21:34:29",
          "fillingStopTime": "2001",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2599",
          "coagulationStartTime": "3257",
          "coagulationStopTime": "5209",
          "cuttingStartTime": "5272",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5457",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5846",
          "cookingStopTime": "7970",
          "cookingRPM": "5",
          "cookingTemperature": "38.7",
          "emptyingStartTime": "9781",
          "emptyingStopTime": "12044",
          "residenceTimeInVat": "7780",
          "totalProcesstimeInVat": "12044",
          "coagulationDuration": "1952",
          "cookingDuration": "2124",
          "emptyingDuration": "2263",
          "vatpH": "6.4"
      },
      {
          "vatNo": "5",
          "sequenceNo": "5",
          "recipeNo": "1",
          "DateTime": "2025-04-18 22:07:50",
          "fillingStopTime": "1999",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "31.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2599",
          "coagulationStartTime": "2931",
          "coagulationStopTime": "5012",
          "cuttingStartTime": "5073",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5253",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5647",
          "cookingStopTime": "7708",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "9643",
          "emptyingStopTime": "11865",
          "residenceTimeInVat": "7644",
          "totalProcesstimeInVat": "11865",
          "coagulationDuration": "2081",
          "cookingDuration": "2061",
          "emptyingDuration": "2222",
          "vatpH": "6.46"
      },
      {
          "vatNo": "6",
          "sequenceNo": "6",
          "recipeNo": "1",
          "DateTime": "2025-04-18 22:41:11",
          "fillingStopTime": "2005",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "303",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2599",
          "coagulationStartTime": "2959",
          "coagulationStopTime": "5029",
          "cuttingStartTime": "5091",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5277",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5674",
          "cookingStopTime": "7726",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9562",
          "emptyingStopTime": "11807",
          "residenceTimeInVat": "7557",
          "totalProcesstimeInVat": "11807",
          "coagulationDuration": "2070",
          "cookingDuration": "2052",
          "emptyingDuration": "2245",
          "vatpH": "6.42"
      },
      {
          "vatNo": "7",
          "sequenceNo": "7",
          "recipeNo": "1",
          "DateTime": "2025-04-18 23:14:36",
          "fillingStopTime": "2009",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "301",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2608",
          "coagulationStartTime": "2949",
          "coagulationStopTime": "5016",
          "cuttingStartTime": "5078",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5258",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5652",
          "cookingStopTime": "7714",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9549",
          "emptyingStopTime": "11825",
          "residenceTimeInVat": "7540",
          "totalProcesstimeInVat": "11825",
          "coagulationDuration": "2067",
          "cookingDuration": "2062",
          "emptyingDuration": "2276",
          "vatpH": "6.43"
      },
      {
          "vatNo": "1",
          "sequenceNo": "8",
          "recipeNo": "1",
          "DateTime": "2025-04-18 23:48:46",
          "fillingStopTime": "1976",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "284",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2635",
          "coagulationStartTime": "2933",
          "coagulationStopTime": "5005",
          "cuttingStartTime": "5068",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5247",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5641",
          "cookingStopTime": "7701",
          "cookingRPM": "5",
          "cookingTemperature": "39.4",
          "emptyingStartTime": "9603",
          "emptyingStopTime": "12168",
          "residenceTimeInVat": "7627",
          "totalProcesstimeInVat": "12168",
          "coagulationDuration": "2072",
          "cookingDuration": "2060",
          "emptyingDuration": "2565",
          "vatpH": "6.47"
      },
      {
          "vatNo": "2",
          "sequenceNo": "9",
          "recipeNo": "1",
          "DateTime": "2025-04-19 00:24:03",
          "fillingStopTime": "2091",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "301",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2687",
          "coagulationStartTime": "3056",
          "coagulationStopTime": "5122",
          "cuttingStartTime": "5185",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5364",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5759",
          "cookingStopTime": "7975",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9805",
          "emptyingStopTime": "11866",
          "residenceTimeInVat": "7714",
          "totalProcesstimeInVat": "11866",
          "coagulationDuration": "2066",
          "cookingDuration": "2216",
          "emptyingDuration": "2061",
          "vatpH": "6.44"
      },
      {
          "vatNo": "4",
          "sequenceNo": "11",
          "recipeNo": "1",
          "DateTime": "2025-04-19 01:32:53",
          "fillingStopTime": "1977",
          "fillingRPM": "5",
          "milkQuantityInVat": "10984",
          "temperatureOfMilkInVat": "34.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2578",
          "coagulationStartTime": "2904",
          "coagulationStopTime": "4990",
          "cuttingStartTime": "5052",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5231",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5626",
          "cookingStopTime": "7832",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9662",
          "emptyingStopTime": "11784",
          "residenceTimeInVat": "7685",
          "totalProcesstimeInVat": "11784",
          "coagulationDuration": "2086",
          "cookingDuration": "2206",
          "emptyingDuration": "2122",
          "vatpH": "6.44"
      },
      {
          "vatNo": "6",
          "sequenceNo": "13",
          "recipeNo": "1",
          "DateTime": "2025-04-19 02:39:21",
          "fillingStopTime": "1980",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2581",
          "coagulationStartTime": "2962",
          "coagulationStopTime": "4980",
          "cuttingStartTime": "5067",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5246",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5640",
          "cookingStopTime": "7709",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9542",
          "emptyingStopTime": "11757",
          "residenceTimeInVat": "7562",
          "totalProcesstimeInVat": "11757",
          "coagulationDuration": "2018",
          "cookingDuration": "2069",
          "emptyingDuration": "2215",
          "vatpH": "6.42"
      },
      {
          "vatNo": "7",
          "sequenceNo": "14",
          "recipeNo": "1",
          "DateTime": "2025-04-19 03:12:36",
          "fillingStopTime": "1992",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "288",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2590",
          "coagulationStartTime": "2935",
          "coagulationStopTime": "5001",
          "cuttingStartTime": "5064",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5243",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5638",
          "cookingStopTime": "7729",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9631",
          "emptyingStopTime": "11700",
          "residenceTimeInVat": "7639",
          "totalProcesstimeInVat": "11700",
          "coagulationDuration": "2066",
          "cookingDuration": "2091",
          "emptyingDuration": "2069",
          "vatpH": "6.42"
      },
      {
          "vatNo": "1",
          "sequenceNo": "15",
          "recipeNo": "2",
          "DateTime": "2025-04-19 03:46:02",
          "fillingStopTime": "1989",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "32.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "896",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2041",
          "coagulationStartTime": "2362",
          "coagulationStopTime": "4434",
          "cuttingStartTime": "4497",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4676",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5070",
          "cookingStopTime": "7131",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "9268",
          "emptyingStopTime": "11749",
          "residenceTimeInVat": "7279",
          "totalProcesstimeInVat": "11749",
          "coagulationDuration": "2072",
          "cookingDuration": "2061",
          "emptyingDuration": "2481",
          "vatpH": "6.41"
      },
      {
          "vatNo": "2",
          "sequenceNo": "16",
          "recipeNo": "2",
          "DateTime": "2025-04-19 04:19:26",
          "fillingStopTime": "1986",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "896",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2036",
          "coagulationStartTime": "2350",
          "coagulationStopTime": "4416",
          "cuttingStartTime": "4479",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4659",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5023",
          "cookingStopTime": "7223",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9396",
          "emptyingStopTime": "11371",
          "residenceTimeInVat": "7410",
          "totalProcesstimeInVat": "11371",
          "coagulationDuration": "2066",
          "cookingDuration": "2200",
          "emptyingDuration": "1975",
          "vatpH": "6.44"
      },
      {
          "vatNo": "3",
          "sequenceNo": "17",
          "recipeNo": "2",
          "DateTime": "2025-04-19 04:55:23",
          "fillingStopTime": "2006",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "898",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2056",
          "coagulationStartTime": "2394",
          "coagulationStopTime": "4479",
          "cuttingStartTime": "4542",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4721",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5115",
          "cookingStopTime": "7176",
          "cookingRPM": "5",
          "cookingTemperature": "39.3",
          "emptyingStartTime": "8952",
          "emptyingStopTime": "11021",
          "residenceTimeInVat": "6946",
          "totalProcesstimeInVat": "11021",
          "coagulationDuration": "2085",
          "cookingDuration": "2061",
          "emptyingDuration": "2069",
          "vatpH": "6.44"
      },
      {
          "vatNo": "4",
          "sequenceNo": "18",
          "recipeNo": "2",
          "DateTime": "2025-04-19 05:29:05",
          "fillingStopTime": "1981",
          "fillingRPM": "5",
          "milkQuantityInVat": "10936",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "896",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2036",
          "coagulationStartTime": "2356",
          "coagulationStopTime": "4434",
          "cuttingStartTime": "4497",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4677",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5071",
          "cookingStopTime": "7192",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8781",
          "emptyingStopTime": "11248",
          "residenceTimeInVat": "6800",
          "totalProcesstimeInVat": "11248",
          "coagulationDuration": "2078",
          "cookingDuration": "2121",
          "emptyingDuration": "2467",
          "vatpH": "6.47"
      },
      {
          "vatNo": "5",
          "sequenceNo": "19",
          "recipeNo": "2",
          "DateTime": "2025-04-19 06:02:07",
          "fillingStopTime": "2016",
          "fillingRPM": "5",
          "milkQuantityInVat": "11263",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "905",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2057",
          "coagulationStartTime": "2371",
          "coagulationStopTime": "4450",
          "cuttingStartTime": "4512",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4692",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5195",
          "cookingStopTime": "7161",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9149",
          "emptyingStopTime": "10693",
          "residenceTimeInVat": "7133",
          "totalProcesstimeInVat": "10693",
          "coagulationDuration": "2079",
          "cookingDuration": "1966",
          "emptyingDuration": "1544",
          "vatpH": "6.46"
      },
      {
          "vatNo": "6",
          "sequenceNo": "20",
          "recipeNo": "2",
          "DateTime": "2025-04-19 06:35:43",
          "fillingStopTime": "1981",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "895",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2034",
          "coagulationStartTime": "2354",
          "coagulationStopTime": "4438",
          "cuttingStartTime": "4572",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4747",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5142",
          "cookingStopTime": "7217",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8863",
          "emptyingStopTime": "11593",
          "residenceTimeInVat": "6882",
          "totalProcesstimeInVat": "11593",
          "coagulationDuration": "2084",
          "cookingDuration": "2075",
          "emptyingDuration": "2730",
          "vatpH": "6.48"
      },
      {
          "vatNo": "7",
          "sequenceNo": "21",
          "recipeNo": "2",
          "DateTime": "2025-04-19 07:09:12",
          "fillingStopTime": "1981",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "878",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2035",
          "coagulationStartTime": "2350",
          "coagulationStopTime": "4429",
          "cuttingStartTime": "4759",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4926",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5308",
          "cookingStopTime": "7380",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9203",
          "emptyingStopTime": "11352",
          "residenceTimeInVat": "7222",
          "totalProcesstimeInVat": "11352",
          "coagulationDuration": "2079",
          "cookingDuration": "2072",
          "emptyingDuration": "2149",
          "vatpH": "6.44"
      },
      {
          "vatNo": "1",
          "sequenceNo": "22",
          "recipeNo": "3",
          "DateTime": "2025-04-19 07:42:14",
          "fillingStopTime": "2015",
          "fillingRPM": "5",
          "milkQuantityInVat": "11615",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1456",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2056",
          "coagulationStartTime": "2466",
          "coagulationStopTime": "4546",
          "cuttingStartTime": "4625",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4799",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5189",
          "cookingStopTime": "7263",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9153",
          "emptyingStopTime": "11067",
          "residenceTimeInVat": "7138",
          "totalProcesstimeInVat": "11067",
          "coagulationDuration": "2080",
          "cookingDuration": "2074",
          "emptyingDuration": "1914",
          "vatpH": "6.44"
      },
      {
          "vatNo": "2",
          "sequenceNo": "23",
          "recipeNo": "3",
          "DateTime": "2025-04-19 08:15:49",
          "fillingStopTime": "2010",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1449",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2063",
          "coagulationStartTime": "2383",
          "coagulationStopTime": "4781",
          "cuttingStartTime": "4906",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4781",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5088",
          "cookingStopTime": "7341",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "8851",
          "emptyingStopTime": "11170",
          "residenceTimeInVat": "6841",
          "totalProcesstimeInVat": "11170",
          "coagulationDuration": "2398",
          "cookingDuration": "2253",
          "emptyingDuration": "2319",
          "vatpH": "6.46"
      },
      {
          "vatNo": "3",
          "sequenceNo": "24",
          "recipeNo": "3",
          "DateTime": "2025-04-19 08:49:19",
          "fillingStopTime": "1999",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "34",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1450",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2061",
          "coagulationStartTime": "2771",
          "coagulationStopTime": "4447",
          "cuttingStartTime": "4513",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4687",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5078",
          "cookingStopTime": "7139",
          "cookingRPM": "5",
          "cookingTemperature": "40",
          "emptyingStartTime": "8638",
          "emptyingStopTime": "10980",
          "residenceTimeInVat": "6639",
          "totalProcesstimeInVat": "10980",
          "coagulationDuration": "1676",
          "cookingDuration": "2061",
          "emptyingDuration": "2342",
          "vatpH": "6.42"
      },
      {
          "vatNo": "4",
          "sequenceNo": "25",
          "recipeNo": "3",
          "DateTime": "2025-04-19 09:23:20",
          "fillingStopTime": "1965",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1410",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2016",
          "coagulationStartTime": "2352",
          "coagulationStopTime": "4421",
          "cuttingStartTime": "4495",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4674",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5067",
          "cookingStopTime": "7154",
          "cookingRPM": "5",
          "cookingTemperature": "38.8",
          "emptyingStartTime": "8544",
          "emptyingStopTime": "11109",
          "residenceTimeInVat": "6579",
          "totalProcesstimeInVat": "11109",
          "coagulationDuration": "2069",
          "cookingDuration": "2087",
          "emptyingDuration": "2565",
          "vatpH": "6.45"
      },
      {
          "vatNo": "5",
          "sequenceNo": "26",
          "recipeNo": "3",
          "DateTime": "2025-04-19 09:56:05",
          "fillingStopTime": "2005",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1458",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2055",
          "coagulationStartTime": "2381",
          "coagulationStopTime": "4469",
          "cuttingStartTime": "4530",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4715",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5096",
          "cookingStopTime": "7169",
          "cookingRPM": "5",
          "cookingTemperature": "39.3",
          "emptyingStartTime": "8652",
          "emptyingStopTime": "10857",
          "residenceTimeInVat": "6647",
          "totalProcesstimeInVat": "10857",
          "coagulationDuration": "2088",
          "cookingDuration": "2073",
          "emptyingDuration": "2205",
          "vatpH": "6.44"
      },
      {
          "vatNo": "6",
          "sequenceNo": "27",
          "recipeNo": "3",
          "DateTime": "2025-04-19 10:29:30",
          "fillingStopTime": "2009",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1448",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2055",
          "coagulationStartTime": "2393",
          "coagulationStopTime": "4477",
          "cuttingStartTime": "4556",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4729",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5117",
          "cookingStopTime": "7175",
          "cookingRPM": "5",
          "cookingTemperature": "39.3",
          "emptyingStartTime": "8574",
          "emptyingStopTime": "10784",
          "residenceTimeInVat": "6565",
          "totalProcesstimeInVat": "10784",
          "coagulationDuration": "2084",
          "cookingDuration": "2058",
          "emptyingDuration": "2210",
          "vatpH": "6.42"
      },
      {
          "vatNo": "2",
          "sequenceNo": "30",
          "recipeNo": "4",
          "DateTime": "2025-04-19 12:09:29",
          "fillingStopTime": "2016",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1818",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2125",
          "coagulationStartTime": "2435",
          "coagulationStopTime": "4501",
          "cuttingStartTime": "4561",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4785",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5134",
          "cookingStopTime": "7304",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8458",
          "emptyingStopTime": "10539",
          "residenceTimeInVat": "6442",
          "totalProcesstimeInVat": "10539",
          "coagulationDuration": "2066",
          "cookingDuration": "2170",
          "emptyingDuration": "2081",
          "vatpH": "6.4"
      },
      {
          "vatNo": "3",
          "sequenceNo": "31",
          "recipeNo": "4",
          "DateTime": "2025-04-19 12:43:05",
          "fillingStopTime": "2003",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1810",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2063",
          "coagulationStartTime": "2377",
          "coagulationStopTime": "4437",
          "cuttingStartTime": "4497",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4683",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5083",
          "cookingStopTime": "7134",
          "cookingRPM": "5",
          "cookingTemperature": "39.5",
          "emptyingStartTime": "8292",
          "emptyingStopTime": "10554",
          "residenceTimeInVat": "6289",
          "totalProcesstimeInVat": "10554",
          "coagulationDuration": "2060",
          "cookingDuration": "2051",
          "emptyingDuration": "2262",
          "vatpH": "31"
      },
      {
          "vatNo": "4",
          "sequenceNo": "32",
          "recipeNo": "4",
          "DateTime": "2025-04-19 13:16:30",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1830",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2055",
          "coagulationStartTime": "2374",
          "coagulationStopTime": "4452",
          "cuttingStartTime": "4511",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4700",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5089",
          "cookingStopTime": "7230",
          "cookingRPM": "5",
          "cookingTemperature": "38.8",
          "emptyingStartTime": "8328",
          "emptyingStopTime": "10787",
          "residenceTimeInVat": "6326",
          "totalProcesstimeInVat": "10787",
          "coagulationDuration": "2078",
          "cookingDuration": "2141",
          "emptyingDuration": "2459",
          "vatpH": "6.4"
      },
      {
          "vatNo": "5",
          "sequenceNo": "33",
          "recipeNo": "4",
          "DateTime": "2025-04-19 13:50:07",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11263",
          "temperatureOfMilkInVat": "32.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1809",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2059",
          "coagulationStartTime": "2383",
          "coagulationStopTime": "4450",
          "cuttingStartTime": "4558",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4729",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5115",
          "cookingStopTime": "7181",
          "cookingRPM": "5",
          "cookingTemperature": "39.3",
          "emptyingStartTime": "8541",
          "emptyingStopTime": "10583",
          "residenceTimeInVat": "6539",
          "totalProcesstimeInVat": "10583",
          "coagulationDuration": "2067",
          "cookingDuration": "2066",
          "emptyingDuration": "2042",
          "vatpH": "6.4"
      },
      {
          "vatNo": "6",
          "sequenceNo": "34",
          "recipeNo": "4",
          "DateTime": "2025-04-19 14:23:29",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1822",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2045",
          "coagulationStartTime": "2387",
          "coagulationStopTime": "4469",
          "cuttingStartTime": "4570",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4734",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5139",
          "cookingStopTime": "7191",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "8349",
          "emptyingStopTime": "10506",
          "residenceTimeInVat": "6358",
          "totalProcesstimeInVat": "10506",
          "coagulationDuration": "2082",
          "cookingDuration": "2052",
          "emptyingDuration": "2157",
          "vatpH": "6.41"
      },
      {
          "vatNo": "1",
          "sequenceNo": "1",
          "recipeNo": "1",
          "DateTime": "2025-04-19 22:52:40",
          "fillingStopTime": "2176",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "311",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2631",
          "coagulationStartTime": "2952",
          "coagulationStopTime": "5024",
          "cuttingStartTime": "5089",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5274",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5660",
          "cookingStopTime": "7723",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9559",
          "emptyingStopTime": "11874",
          "residenceTimeInVat": "7383",
          "totalProcesstimeInVat": "11874",
          "coagulationDuration": "2072",
          "cookingDuration": "2063",
          "emptyingDuration": "2315",
          "vatpH": "6.49"
      },
      {
          "vatNo": "2",
          "sequenceNo": "2",
          "recipeNo": "1",
          "DateTime": "2025-04-19 23:28:56",
          "fillingStopTime": "1802",
          "fillingRPM": "5",
          "milkQuantityInVat": "10936",
          "temperatureOfMilkInVat": "31.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "103",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2394",
          "coagulationStartTime": "2718",
          "coagulationStopTime": "4788",
          "cuttingStartTime": "4850",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5029",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5424",
          "cookingStopTime": "7620",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9452",
          "emptyingStopTime": "11590",
          "residenceTimeInVat": "7650",
          "totalProcesstimeInVat": "11590",
          "coagulationDuration": "2070",
          "cookingDuration": "2196",
          "emptyingDuration": "2138",
          "vatpH": "6.46"
      },
      {
          "vatNo": "3",
          "sequenceNo": "3",
          "recipeNo": "1",
          "DateTime": "2025-04-19 23:58:58",
          "fillingStopTime": "2006",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "296",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2605",
          "coagulationStartTime": "2925",
          "coagulationStopTime": "4997",
          "cuttingStartTime": "5060",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5240",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5635",
          "cookingStopTime": "7791",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9621",
          "emptyingStopTime": "11798",
          "residenceTimeInVat": "7615",
          "totalProcesstimeInVat": "11798",
          "coagulationDuration": "2072",
          "cookingDuration": "2156",
          "emptyingDuration": "2177",
          "vatpH": "6.52"
      },
      {
          "vatNo": "4",
          "sequenceNo": "4",
          "recipeNo": "1",
          "DateTime": "2025-04-20 00:32:25",
          "fillingStopTime": "2132",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "304",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2728",
          "coagulationStartTime": "3054",
          "coagulationStopTime": "5115",
          "cuttingStartTime": "5177",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5357",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5751",
          "cookingStopTime": "7815",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9646",
          "emptyingStopTime": "11911",
          "residenceTimeInVat": "7514",
          "totalProcesstimeInVat": "11911",
          "coagulationDuration": "2061",
          "cookingDuration": "2064",
          "emptyingDuration": "2265",
          "vatpH": "6.51"
      },
      {
          "vatNo": "5",
          "sequenceNo": "5",
          "recipeNo": "1",
          "DateTime": "2025-04-20 01:08:13",
          "fillingStopTime": "1995",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "31.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "289",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2592",
          "coagulationStartTime": "2924",
          "coagulationStopTime": "5072",
          "cuttingStartTime": "5246",
          "cuttingRPM": "7",
          "intermittentCuttingStartTime": "5246",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5648",
          "cookingStopTime": "7700",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9534",
          "emptyingStopTime": "11765",
          "residenceTimeInVat": "7539",
          "totalProcesstimeInVat": "11765",
          "coagulationDuration": "2148",
          "cookingDuration": "2052",
          "emptyingDuration": "2231",
          "vatpH": "6.5"
      },
      {
          "vatNo": "6",
          "sequenceNo": "6",
          "recipeNo": "1",
          "DateTime": "2025-04-20 01:41:29",
          "fillingStopTime": "2006",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "301",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2604",
          "coagulationStartTime": "3076",
          "coagulationStopTime": "5033",
          "cuttingStartTime": "5096",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5276",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5670",
          "cookingStopTime": "7730",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9564",
          "emptyingStopTime": "11922",
          "residenceTimeInVat": "7558",
          "totalProcesstimeInVat": "11922",
          "coagulationDuration": "1957",
          "cookingDuration": "2060",
          "emptyingDuration": "2358",
          "vatpH": "6.5"
      },
      {
          "vatNo": "7",
          "sequenceNo": "7",
          "recipeNo": "1",
          "DateTime": "2025-04-20 02:15:23",
          "fillingStopTime": "1961",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "276",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2559",
          "coagulationStartTime": "2903",
          "coagulationStopTime": "4994",
          "cuttingStartTime": "5057",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5236",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5636",
          "cookingStopTime": "7728",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9562",
          "emptyingStopTime": "11684",
          "residenceTimeInVat": "7601",
          "totalProcesstimeInVat": "11684",
          "coagulationDuration": "2091",
          "cookingDuration": "2092",
          "emptyingDuration": "2122",
          "vatpH": "6.48"
      },
      {
          "vatNo": "1",
          "sequenceNo": "8",
          "recipeNo": "1",
          "DateTime": "2025-04-20 02:48:05",
          "fillingStopTime": "2000",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "300",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2597",
          "coagulationStartTime": "2923",
          "coagulationStopTime": "4995",
          "cuttingStartTime": "5058",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5238",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5632",
          "cookingStopTime": "7692",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9527",
          "emptyingStopTime": "12187",
          "residenceTimeInVat": "7527",
          "totalProcesstimeInVat": "12187",
          "coagulationDuration": "2072",
          "cookingDuration": "2060",
          "emptyingDuration": "2660",
          "vatpH": "6.49"
      },
      {
          "vatNo": "2",
          "sequenceNo": "9",
          "recipeNo": "1",
          "DateTime": "2025-04-20 03:21:39",
          "fillingStopTime": "1995",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "292",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2595",
          "coagulationStartTime": "2909",
          "coagulationStopTime": "4993",
          "cuttingStartTime": "5056",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5236",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5630",
          "cookingStopTime": "7766",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9950",
          "emptyingStopTime": "11876",
          "residenceTimeInVat": "7955",
          "totalProcesstimeInVat": "11876",
          "coagulationDuration": "2084",
          "cookingDuration": "2136",
          "emptyingDuration": "1926",
          "vatpH": "6.45"
      },
      {
          "vatNo": "3",
          "sequenceNo": "10",
          "recipeNo": "1",
          "DateTime": "2025-04-20 03:55:10",
          "fillingStopTime": "1995",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "287",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2596",
          "coagulationStartTime": "2910",
          "coagulationStopTime": "4982",
          "cuttingStartTime": "5045",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5225",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5619",
          "cookingStopTime": "7679",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9647",
          "emptyingStopTime": "11745",
          "residenceTimeInVat": "7652",
          "totalProcesstimeInVat": "11745",
          "coagulationDuration": "2072",
          "cookingDuration": "2060",
          "emptyingDuration": "2098",
          "vatpH": "6.41"
      },
      {
          "vatNo": "5",
          "sequenceNo": "12",
          "recipeNo": "1",
          "DateTime": "2025-04-20 05:02:00",
          "fillingStopTime": "1988",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "32.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "286",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2583",
          "coagulationStartTime": "2916",
          "coagulationStopTime": "4988",
          "cuttingStartTime": "5094",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5230",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5625",
          "cookingStopTime": "7686",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9554",
          "emptyingStopTime": "11817",
          "residenceTimeInVat": "7566",
          "totalProcesstimeInVat": "11817",
          "coagulationDuration": "2072",
          "cookingDuration": "2061",
          "emptyingDuration": "2263",
          "vatpH": "6.4"
      },
      {
          "vatNo": "6",
          "sequenceNo": "13",
          "recipeNo": "1",
          "DateTime": "2025-04-20 05:36:21",
          "fillingStopTime": "2000",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "299",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2596",
          "coagulationStartTime": "2922",
          "coagulationStopTime": "5001",
          "cuttingStartTime": "5063",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5243",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5637",
          "cookingStopTime": "7700",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9572",
          "emptyingStopTime": "11837",
          "residenceTimeInVat": "7572",
          "totalProcesstimeInVat": "11837",
          "coagulationDuration": "2079",
          "cookingDuration": "2063",
          "emptyingDuration": "2265",
          "vatpH": "6.33"
      },
      {
          "vatNo": "7",
          "sequenceNo": "14",
          "recipeNo": "1",
          "DateTime": "2025-04-20 06:10:31",
          "fillingStopTime": "2013",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "299",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2614",
          "coagulationStartTime": "2964",
          "coagulationStopTime": "5025",
          "cuttingStartTime": "5098",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5272",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5666",
          "cookingStopTime": "7722",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9610",
          "emptyingStopTime": "11504",
          "residenceTimeInVat": "7597",
          "totalProcesstimeInVat": "11504",
          "coagulationDuration": "2061",
          "cookingDuration": "2056",
          "emptyingDuration": "1894",
          "vatpH": "6.36"
      },
      {
          "vatNo": "1",
          "sequenceNo": "15",
          "recipeNo": "2",
          "DateTime": "2025-04-20 06:45:12",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "908",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2053",
          "coagulationStartTime": "2374",
          "coagulationStopTime": "4452",
          "cuttingStartTime": "4518",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4701",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5098",
          "cookingStopTime": "7159",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9252",
          "emptyingStopTime": "11338",
          "residenceTimeInVat": "7250",
          "totalProcesstimeInVat": "11338",
          "coagulationDuration": "2078",
          "cookingDuration": "2061",
          "emptyingDuration": "2086",
          "vatpH": "6.39"
      },
      {
          "vatNo": "2",
          "sequenceNo": "16",
          "recipeNo": "2",
          "DateTime": "2025-04-20 07:18:49",
          "fillingStopTime": "1992",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "897",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2055",
          "coagulationStartTime": "2375",
          "coagulationStopTime": "4452",
          "cuttingStartTime": "4520",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4695",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5060",
          "cookingStopTime": "7235",
          "cookingRPM": "5",
          "cookingTemperature": "38.4",
          "emptyingStartTime": "9112",
          "emptyingStopTime": "11015",
          "residenceTimeInVat": "7120",
          "totalProcesstimeInVat": "11015",
          "coagulationDuration": "2077",
          "cookingDuration": "2175",
          "emptyingDuration": "1903",
          "vatpH": "6.36"
      },
      {
          "vatNo": "3",
          "sequenceNo": "17",
          "recipeNo": "2",
          "DateTime": "2025-04-20 07:52:01",
          "fillingStopTime": "2205",
          "fillingRPM": "4",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "907",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2205",
          "coagulationStartTime": "2442",
          "coagulationStopTime": "4515",
          "cuttingStartTime": "4578",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4762",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5153",
          "cookingStopTime": "7212",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8828",
          "emptyingStopTime": "11095",
          "residenceTimeInVat": "6623",
          "totalProcesstimeInVat": "11095",
          "coagulationDuration": "2073",
          "cookingDuration": "2059",
          "emptyingDuration": "2267",
          "vatpH": "6.33"
      },
      {
          "vatNo": "4",
          "sequenceNo": "18",
          "recipeNo": "2",
          "DateTime": "2025-04-20 08:28:46",
          "fillingStopTime": "1903",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "715",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1964",
          "coagulationStartTime": "2277",
          "coagulationStopTime": "4345",
          "cuttingStartTime": "4420",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4586",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4980",
          "cookingStopTime": "7194",
          "cookingRPM": "5",
          "cookingTemperature": "38.5",
          "emptyingStartTime": "8709",
          "emptyingStopTime": "10884",
          "residenceTimeInVat": "6806",
          "totalProcesstimeInVat": "10884",
          "coagulationDuration": "2068",
          "cookingDuration": "2214",
          "emptyingDuration": "2175",
          "vatpH": "6.31"
      },
      {
          "vatNo": "5",
          "sequenceNo": "19",
          "recipeNo": "2",
          "DateTime": "2025-04-20 09:00:59",
          "fillingStopTime": "1975",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32",
          "cultureQuantity": "200",
          "cultureAdditionTime": "885",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2026",
          "coagulationStartTime": "2351",
          "coagulationStopTime": "4417",
          "cuttingStartTime": "4480",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4669",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5066",
          "cookingStopTime": "7115",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "8750",
          "emptyingStopTime": "12512",
          "residenceTimeInVat": "6775",
          "totalProcesstimeInVat": "12512",
          "coagulationDuration": "2066",
          "cookingDuration": "2049",
          "emptyingDuration": "3762",
          "vatpH": "6.34"
      },
      {
          "vatNo": "6",
          "sequenceNo": "20",
          "recipeNo": "2",
          "DateTime": "2025-04-20 09:33:54",
          "fillingStopTime": "1996",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32",
          "cultureQuantity": "200",
          "cultureAdditionTime": "899",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2050",
          "coagulationStartTime": "2366",
          "coagulationStopTime": "4441",
          "cuttingStartTime": "4513",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4680",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5079",
          "cookingStopTime": "7134",
          "cookingRPM": "5",
          "cookingTemperature": "39.4",
          "emptyingStartTime": "8819",
          "emptyingStopTime": "11512",
          "residenceTimeInVat": "6823",
          "totalProcesstimeInVat": "11512",
          "coagulationDuration": "2075",
          "cookingDuration": "2055",
          "emptyingDuration": "2693",
          "vatpH": "6.36"
      },
      {
          "vatNo": "7",
          "sequenceNo": "21",
          "recipeNo": "2",
          "DateTime": "2025-04-20 10:07:27",
          "fillingStopTime": "2005",
          "fillingRPM": "5",
          "milkQuantityInVat": "11263",
          "temperatureOfMilkInVat": "32.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "897",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2064",
          "coagulationStartTime": "2383",
          "coagulationStopTime": "4474",
          "cuttingStartTime": "4590",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4762",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5155",
          "cookingStopTime": "7221",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9499",
          "emptyingStopTime": "10898",
          "residenceTimeInVat": "7494",
          "totalProcesstimeInVat": "10898",
          "coagulationDuration": "2091",
          "cookingDuration": "2066",
          "emptyingDuration": "1399",
          "vatpH": "6.34"
      },
      {
          "vatNo": "1",
          "sequenceNo": "22",
          "recipeNo": "3",
          "DateTime": "2025-04-20 10:40:52",
          "fillingStopTime": "2008",
          "fillingRPM": "5",
          "milkQuantityInVat": "11615",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1445",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2060",
          "coagulationStartTime": "2366",
          "coagulationStopTime": "4439",
          "cuttingStartTime": "4506",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4690",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5087",
          "cookingStopTime": "7154",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8893",
          "emptyingStopTime": "11003",
          "residenceTimeInVat": "6885",
          "totalProcesstimeInVat": "11003",
          "coagulationDuration": "2073",
          "cookingDuration": "2067",
          "emptyingDuration": "2110",
          "vatpH": "6.37"
      },
      {
          "vatNo": "2",
          "sequenceNo": "23",
          "recipeNo": "3",
          "DateTime": "2025-04-20 11:14:20",
          "fillingStopTime": "1999",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1455",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2051",
          "coagulationStartTime": "2409",
          "coagulationStopTime": "4511",
          "cuttingStartTime": "4547",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4740",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5127",
          "cookingStopTime": "7355",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "8845",
          "emptyingStopTime": "11074",
          "residenceTimeInVat": "6846",
          "totalProcesstimeInVat": "11074",
          "coagulationDuration": "2102",
          "cookingDuration": "2228",
          "emptyingDuration": "2229",
          "vatpH": "6.35"
      },
      {
          "vatNo": "3",
          "sequenceNo": "24",
          "recipeNo": "3",
          "DateTime": "2025-04-20 11:47:58",
          "fillingStopTime": "1989",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "31.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1441",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2054",
          "coagulationStartTime": "2446",
          "coagulationStopTime": "4447",
          "cuttingStartTime": "4499",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4679",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5080",
          "cookingStopTime": "7195",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8791",
          "emptyingStopTime": "10816",
          "residenceTimeInVat": "6802",
          "totalProcesstimeInVat": "10816",
          "coagulationDuration": "2001",
          "cookingDuration": "2115",
          "emptyingDuration": "2025",
          "vatpH": "6.39"
      },
      {
          "vatNo": "4",
          "sequenceNo": "25",
          "recipeNo": "3",
          "DateTime": "2025-04-20 12:21:08",
          "fillingStopTime": "2014",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1461",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2068",
          "coagulationStartTime": "2390",
          "coagulationStopTime": "4444",
          "cuttingStartTime": "4520",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4700",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5080",
          "cookingStopTime": "7206",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "8612",
          "emptyingStopTime": "10946",
          "residenceTimeInVat": "6598",
          "totalProcesstimeInVat": "10946",
          "coagulationDuration": "2054",
          "cookingDuration": "2126",
          "emptyingDuration": "2334",
          "vatpH": "6.45"
      },
      {
          "vatNo": "5",
          "sequenceNo": "26",
          "recipeNo": "3",
          "DateTime": "2025-04-20 12:54:42",
          "fillingStopTime": "2032",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "35.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1444",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2077",
          "coagulationStartTime": "2374",
          "coagulationStopTime": "4450",
          "cuttingStartTime": "4508",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4679",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5073",
          "cookingStopTime": "7280",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8693",
          "emptyingStopTime": "10948",
          "residenceTimeInVat": "6661",
          "totalProcesstimeInVat": "10948",
          "coagulationDuration": "2076",
          "cookingDuration": "2207",
          "emptyingDuration": "2255",
          "vatpH": "6.3"
      },
      {
          "vatNo": "6",
          "sequenceNo": "27",
          "recipeNo": "3",
          "DateTime": "2025-04-20 13:28:34",
          "fillingStopTime": "1969",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1418",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2024",
          "coagulationStartTime": "2362",
          "coagulationStopTime": "4436",
          "cuttingStartTime": "4495",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4679",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5071",
          "cookingStopTime": "7360",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8693",
          "emptyingStopTime": "10800",
          "residenceTimeInVat": "6724",
          "totalProcesstimeInVat": "10800",
          "coagulationDuration": "2074",
          "cookingDuration": "2289",
          "emptyingDuration": "2107",
          "vatpH": "6.31"
      },
      {
          "vatNo": "7",
          "sequenceNo": "28",
          "recipeNo": "3",
          "DateTime": "2025-04-20 14:01:23",
          "fillingStopTime": "2009",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1451",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2062",
          "coagulationStartTime": "2378",
          "coagulationStopTime": "4462",
          "cuttingStartTime": "4545",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4722",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5232",
          "cookingStopTime": "7195",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8593",
          "emptyingStopTime": "10559",
          "residenceTimeInVat": "6584",
          "totalProcesstimeInVat": "10559",
          "coagulationDuration": "2084",
          "cookingDuration": "1963",
          "emptyingDuration": "1966",
          "vatpH": "6.35"
      },
      {
          "vatNo": "1",
          "sequenceNo": "29",
          "recipeNo": "4",
          "DateTime": "2025-04-20 14:34:52",
          "fillingStopTime": "2008",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1815",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2058",
          "coagulationStartTime": "2378",
          "coagulationStopTime": "4448",
          "cuttingStartTime": "4509",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4688",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5091",
          "cookingStopTime": "7175",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8333",
          "emptyingStopTime": "10713",
          "residenceTimeInVat": "6325",
          "totalProcesstimeInVat": "10713",
          "coagulationDuration": "2070",
          "cookingDuration": "2084",
          "emptyingDuration": "2380",
          "vatpH": "6.36"
      },
      {
          "vatNo": "2",
          "sequenceNo": "30",
          "recipeNo": "4",
          "DateTime": "2025-04-20 15:08:35",
          "fillingStopTime": "1990",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1810",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2044",
          "coagulationStartTime": "2362",
          "coagulationStopTime": "4436",
          "cuttingStartTime": "4510",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4799",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5085",
          "cookingStopTime": "7280",
          "cookingRPM": "5",
          "cookingTemperature": "38.6",
          "emptyingStartTime": "8457",
          "emptyingStopTime": "10577",
          "residenceTimeInVat": "6467",
          "totalProcesstimeInVat": "10577",
          "coagulationDuration": "2074",
          "cookingDuration": "2195",
          "emptyingDuration": "2120",
          "vatpH": "6.35"
      },
      {
          "vatNo": "3",
          "sequenceNo": "31",
          "recipeNo": "4",
          "DateTime": "2025-04-20 15:41:47",
          "fillingStopTime": "2013",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1836",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2062",
          "coagulationStartTime": "2395",
          "coagulationStopTime": "4481",
          "cuttingStartTime": "4551",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4715",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5109",
          "cookingStopTime": "7169",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8349",
          "emptyingStopTime": "10807",
          "residenceTimeInVat": "6336",
          "totalProcesstimeInVat": "10807",
          "coagulationDuration": "2086",
          "cookingDuration": "2060",
          "emptyingDuration": "2458",
          "vatpH": "6.34"
      },
      {
          "vatNo": "4",
          "sequenceNo": "32",
          "recipeNo": "4",
          "DateTime": "2025-04-20 16:15:20",
          "fillingStopTime": "2001",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1822",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2065",
          "coagulationStartTime": "2391",
          "coagulationStopTime": "4470",
          "cuttingStartTime": "4525",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4715",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5105",
          "cookingStopTime": "7256",
          "cookingRPM": "5",
          "cookingTemperature": "38.8",
          "emptyingStartTime": "8405",
          "emptyingStopTime": "10729",
          "residenceTimeInVat": "6404",
          "totalProcesstimeInVat": "10729",
          "coagulationDuration": "2079",
          "cookingDuration": "2151",
          "emptyingDuration": "2324",
          "vatpH": "6.34"
      },
      {
          "vatNo": "5",
          "sequenceNo": "33",
          "recipeNo": "4",
          "DateTime": "2025-04-20 16:48:57",
          "fillingStopTime": "1986",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "34.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1801",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2032",
          "coagulationStartTime": "2364",
          "coagulationStopTime": "4449",
          "cuttingStartTime": "4499",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4678",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5084",
          "cookingStopTime": "7246",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8399",
          "emptyingStopTime": "10753",
          "residenceTimeInVat": "6413",
          "totalProcesstimeInVat": "10753",
          "coagulationDuration": "2085",
          "cookingDuration": "2162",
          "emptyingDuration": "2354",
          "vatpH": "6.33"
      },
      {
          "vatNo": "6",
          "sequenceNo": "34",
          "recipeNo": "4",
          "DateTime": "2025-04-20 17:22:03",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "34",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1817",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2044",
          "coagulationStartTime": "2463",
          "coagulationStopTime": "4534",
          "cuttingStartTime": "4663",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4837",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5242",
          "cookingStopTime": "7339",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8484",
          "emptyingStopTime": "10741",
          "residenceTimeInVat": "6493",
          "totalProcesstimeInVat": "10741",
          "coagulationDuration": "2071",
          "cookingDuration": "2097",
          "emptyingDuration": "2257",
          "vatpH": "6.33"
      },
      {
          "vatNo": "7",
          "sequenceNo": "35",
          "recipeNo": "4",
          "DateTime": "2025-04-20 17:55:28",
          "fillingStopTime": "2017",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1838",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2066",
          "coagulationStartTime": "2382",
          "coagulationStopTime": "4458",
          "cuttingStartTime": "4694",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4866",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5259",
          "cookingStopTime": "7332",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8472",
          "emptyingStopTime": "10408",
          "residenceTimeInVat": "6455",
          "totalProcesstimeInVat": "10408",
          "coagulationDuration": "2076",
          "cookingDuration": "2073",
          "emptyingDuration": "1936",
          "vatpH": "6.33"
      },
      {
          "vatNo": "1",
          "sequenceNo": "1",
          "recipeNo": "1",
          "DateTime": "2025-04-21 02:02:32",
          "fillingStopTime": "1982",
          "fillingRPM": "5",
          "milkQuantityInVat": "11486",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "304",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2601",
          "coagulationStartTime": "2915",
          "coagulationStopTime": "5176",
          "cuttingStartTime": "5405",
          "cuttingRPM": "7",
          "intermittentCuttingStartTime": "5405",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5642",
          "cookingStopTime": "7703",
          "cookingRPM": "5",
          "cookingTemperature": "39.4",
          "emptyingStartTime": "9538",
          "emptyingStopTime": "11946",
          "residenceTimeInVat": "7556",
          "totalProcesstimeInVat": "11946",
          "coagulationDuration": "2261",
          "cookingDuration": "2061",
          "emptyingDuration": "2408",
          "vatpH": "6.45"
      },
      {
          "vatNo": "2",
          "sequenceNo": "2",
          "recipeNo": "1",
          "DateTime": "2025-04-21 02:35:35",
          "fillingStopTime": "2013",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "304",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2613",
          "coagulationStartTime": "2934",
          "coagulationStopTime": "5024",
          "cuttingStartTime": "5095",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5273",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5667",
          "cookingStopTime": "7875",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9708",
          "emptyingStopTime": "12152",
          "residenceTimeInVat": "7695",
          "totalProcesstimeInVat": "12152",
          "coagulationDuration": "2090",
          "cookingDuration": "2208",
          "emptyingDuration": "2444",
          "vatpH": "6.46"
      },
      {
          "vatNo": "3",
          "sequenceNo": "3",
          "recipeNo": "1",
          "DateTime": "2025-04-21 03:09:36",
          "fillingStopTime": "2044",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "30.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "274",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2824",
          "coagulationStartTime": "3140",
          "coagulationStopTime": "5206",
          "cuttingStartTime": "5269",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5449",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5843",
          "cookingStopTime": "8107",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9895",
          "emptyingStopTime": "11782",
          "residenceTimeInVat": "7851",
          "totalProcesstimeInVat": "11782",
          "coagulationDuration": "2066",
          "cookingDuration": "2264",
          "emptyingDuration": "1887",
          "vatpH": "6.46"
      },
      {
          "vatNo": "4",
          "sequenceNo": "4",
          "recipeNo": "1",
          "DateTime": "2025-04-21 03:43:41",
          "fillingStopTime": "2009",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "301",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2709",
          "coagulationStartTime": "2924",
          "coagulationStopTime": "5003",
          "cuttingStartTime": "5065",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5245",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5639",
          "cookingStopTime": "7726",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "9562",
          "emptyingStopTime": "13094",
          "residenceTimeInVat": "7553",
          "totalProcesstimeInVat": "13094",
          "coagulationDuration": "2079",
          "cookingDuration": "2087",
          "emptyingDuration": "3532",
          "vatpH": "6.46"
      },
      {
          "vatNo": "5",
          "sequenceNo": "5",
          "recipeNo": "1",
          "DateTime": "2025-04-21 04:17:25",
          "fillingStopTime": "1990",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "31.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "285",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2588",
          "coagulationStartTime": "2909",
          "coagulationStopTime": "4987",
          "cuttingStartTime": "5049",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5229",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5623",
          "cookingStopTime": "7684",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "10021",
          "emptyingStopTime": "14806",
          "residenceTimeInVat": "8031",
          "totalProcesstimeInVat": "14806",
          "coagulationDuration": "2078",
          "cookingDuration": "2061",
          "emptyingDuration": "4785",
          "vatpH": "6.47"
      },
      {
          "vatNo": "6",
          "sequenceNo": "6",
          "recipeNo": "1",
          "DateTime": "2025-04-21 04:50:49",
          "fillingStopTime": "1989",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32",
          "cultureQuantity": "200",
          "cultureAdditionTime": "291",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2588",
          "coagulationStartTime": "2896",
          "coagulationStopTime": "4974",
          "cuttingStartTime": "5037",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5218",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5611",
          "cookingStopTime": "7692",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "11405",
          "emptyingStopTime": "15752",
          "residenceTimeInVat": "9416",
          "totalProcesstimeInVat": "15752",
          "coagulationDuration": "2078",
          "cookingDuration": "2081",
          "emptyingDuration": "4347",
          "vatpH": "6.45"
      },
      {
          "vatNo": "7",
          "sequenceNo": "7",
          "recipeNo": "1",
          "DateTime": "2025-04-21 05:24:13",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "312",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2587",
          "coagulationStartTime": "2913",
          "coagulationStopTime": "4992",
          "cuttingStartTime": "5054",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5234",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5628",
          "cookingStopTime": "7690",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "13017",
          "emptyingStopTime": "15166",
          "residenceTimeInVat": "11026",
          "totalProcesstimeInVat": "15166",
          "coagulationDuration": "2079",
          "cookingDuration": "2062",
          "emptyingDuration": "2149",
          "vatpH": "6.31"
      },
      {
          "vatNo": "1",
          "sequenceNo": "8",
          "recipeNo": "1",
          "DateTime": "2025-04-21 05:57:40",
          "fillingStopTime": "1986",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "286",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2582",
          "coagulationStartTime": "2909",
          "coagulationStopTime": "4999",
          "cuttingStartTime": "5056",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5236",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5641",
          "cookingStopTime": "7706",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "13203",
          "emptyingStopTime": "15114",
          "residenceTimeInVat": "11217",
          "totalProcesstimeInVat": "15114",
          "coagulationDuration": "2090",
          "cookingDuration": "2065",
          "emptyingDuration": "1911",
          "vatpH": "6.27"
      },
      {
          "vatNo": "2",
          "sequenceNo": "9",
          "recipeNo": "1",
          "DateTime": "2025-04-21 06:31:00",
          "fillingStopTime": "2006",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "287",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2590",
          "coagulationStartTime": "2922",
          "coagulationStopTime": "4989",
          "cuttingStartTime": "5063",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5231",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5638",
          "cookingStopTime": "7898",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "13197",
          "emptyingStopTime": "15939",
          "residenceTimeInVat": "11191",
          "totalProcesstimeInVat": "15939",
          "coagulationDuration": "2067",
          "cookingDuration": "2260",
          "emptyingDuration": "2742",
          "vatpH": "6.34"
      },
      {
          "vatNo": "3",
          "sequenceNo": "10",
          "recipeNo": "1",
          "DateTime": "2025-04-21 07:15:29",
          "fillingStopTime": "2003",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32",
          "cultureQuantity": "200",
          "cultureAdditionTime": "271",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2646",
          "coagulationStartTime": "2989",
          "coagulationStopTime": "5069",
          "cuttingStartTime": "5136",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5324",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5713",
          "cookingStopTime": "7995",
          "cookingRPM": "5",
          "cookingTemperature": "38.7",
          "emptyingStartTime": "12394",
          "emptyingStopTime": "14084",
          "residenceTimeInVat": "10391",
          "totalProcesstimeInVat": "14084",
          "coagulationDuration": "2080",
          "cookingDuration": "2282",
          "emptyingDuration": "1690",
          "vatpH": "6.4"
      },
      {
          "vatNo": "4",
          "sequenceNo": "11",
          "recipeNo": "1",
          "DateTime": "2025-04-21 08:57:08",
          "fillingStopTime": "2013",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "304",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2659",
          "coagulationStartTime": "2975",
          "coagulationStopTime": "5055",
          "cuttingStartTime": "5123",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5306",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5695",
          "cookingStopTime": "7874",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "9828",
          "emptyingStopTime": "11822",
          "residenceTimeInVat": "7815",
          "totalProcesstimeInVat": "11822",
          "coagulationDuration": "2080",
          "cookingDuration": "2179",
          "emptyingDuration": "1994",
          "vatpH": "6.44"
      },
      {
          "vatNo": "6",
          "sequenceNo": "13",
          "recipeNo": "1",
          "DateTime": "2025-04-21 10:04:05",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "297",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2591",
          "coagulationStartTime": "3012",
          "coagulationStopTime": "5086",
          "cuttingStartTime": "5158",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5330",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5717",
          "cookingStopTime": "7780",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9802",
          "emptyingStopTime": "12053",
          "residenceTimeInVat": "7811",
          "totalProcesstimeInVat": "12053",
          "coagulationDuration": "2074",
          "cookingDuration": "2063",
          "emptyingDuration": "2251",
          "vatpH": "6.42"
      },
      {
          "vatNo": "7",
          "sequenceNo": "14",
          "recipeNo": "1",
          "DateTime": "2025-04-21 10:37:45",
          "fillingStopTime": "1996",
          "fillingRPM": "5",
          "milkQuantityInVat": "11263",
          "temperatureOfMilkInVat": "32.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2581",
          "coagulationStartTime": "3066",
          "coagulationStopTime": "5012",
          "cuttingStartTime": "5070",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5248",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5646",
          "cookingStopTime": "7727",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9736",
          "emptyingStopTime": "11945",
          "residenceTimeInVat": "7740",
          "totalProcesstimeInVat": "11945",
          "coagulationDuration": "1946",
          "cookingDuration": "2081",
          "emptyingDuration": "2209",
          "vatpH": "6.4"
      },
      {
          "vatNo": "2",
          "sequenceNo": "16",
          "recipeNo": "2",
          "DateTime": "2025-04-21 11:44:23",
          "fillingStopTime": "2014",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "901",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2060",
          "coagulationStartTime": "2398",
          "coagulationStopTime": "4458",
          "cuttingStartTime": "4531",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4717",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5070",
          "cookingStopTime": "7284",
          "cookingRPM": "5",
          "cookingTemperature": "38.8",
          "emptyingStartTime": "9413",
          "emptyingStopTime": "11366",
          "residenceTimeInVat": "7399",
          "totalProcesstimeInVat": "11366",
          "coagulationDuration": "2060",
          "cookingDuration": "2214",
          "emptyingDuration": "1953",
          "vatpH": "6.41"
      },
      {
          "vatNo": "4",
          "sequenceNo": "18",
          "recipeNo": "2",
          "DateTime": "2025-04-21 12:51:19",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "910",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2049",
          "coagulationStartTime": "2424",
          "coagulationStopTime": "4512",
          "cuttingStartTime": "4569",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4755",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5143",
          "cookingStopTime": "7310",
          "cookingRPM": "5",
          "cookingTemperature": "38.8",
          "emptyingStartTime": "9143",
          "emptyingStopTime": "12743",
          "residenceTimeInVat": "7152",
          "totalProcesstimeInVat": "12743",
          "coagulationDuration": "2088",
          "cookingDuration": "2167",
          "emptyingDuration": "3600",
          "vatpH": "6.35"
      },
      {
          "vatNo": "5",
          "sequenceNo": "19",
          "recipeNo": "2",
          "DateTime": "2025-04-21 13:26:44",
          "fillingStopTime": "1985",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "33.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "892",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2044",
          "coagulationStartTime": "2366",
          "coagulationStopTime": "4449",
          "cuttingStartTime": "4549",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4718",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5118",
          "cookingStopTime": "7320",
          "cookingRPM": "5",
          "cookingTemperature": "38.5",
          "emptyingStartTime": "10021",
          "emptyingStopTime": "13231",
          "residenceTimeInVat": "8036",
          "totalProcesstimeInVat": "13231",
          "coagulationDuration": "2083",
          "cookingDuration": "2202",
          "emptyingDuration": "3210",
          "vatpH": "6.34"
      },
      {
          "vatNo": "6",
          "sequenceNo": "20",
          "recipeNo": "2",
          "DateTime": "2025-04-21 14:02:03",
          "fillingStopTime": "3799",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "35.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "894",
          "rennetQuantity": "200",
          "rennetAdditionTime": "3842",
          "coagulationStartTime": "4169",
          "coagulationStopTime": "6238",
          "cuttingStartTime": "6309",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "6479",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "6883",
          "cookingStopTime": "8986",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "10617",
          "emptyingStopTime": "13204",
          "residenceTimeInVat": "6818",
          "totalProcesstimeInVat": "13204",
          "coagulationDuration": "2069",
          "cookingDuration": "2103",
          "emptyingDuration": "2587",
          "vatpH": "6.2"
      },
      {
          "vatNo": "7",
          "sequenceNo": "21",
          "recipeNo": "2",
          "DateTime": "2025-04-21 15:05:22",
          "fillingStopTime": "1998",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "34.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "902",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2047",
          "coagulationStartTime": "2424",
          "coagulationStopTime": "4524",
          "cuttingStartTime": "4579",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4764",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5151",
          "cookingStopTime": "7452",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9078",
          "emptyingStopTime": "11083",
          "residenceTimeInVat": "7080",
          "totalProcesstimeInVat": "11083",
          "coagulationDuration": "2100",
          "cookingDuration": "2301",
          "emptyingDuration": "2005",
          "vatpH": "6.31"
      },
      {
          "vatNo": "1",
          "sequenceNo": "22",
          "recipeNo": "3",
          "DateTime": "2025-04-21 15:38:40",
          "fillingStopTime": "2006",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "32",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1447",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2053",
          "coagulationStartTime": "2374",
          "coagulationStopTime": "4455",
          "cuttingStartTime": "4510",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4688",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5082",
          "cookingStopTime": "7150",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "8789",
          "emptyingStopTime": "11134",
          "residenceTimeInVat": "6783",
          "totalProcesstimeInVat": "11134",
          "coagulationDuration": "2081",
          "cookingDuration": "2068",
          "emptyingDuration": "2345",
          "vatpH": "6.41"
      },
      {
          "vatNo": "2",
          "sequenceNo": "23",
          "recipeNo": "3",
          "DateTime": "2025-04-21 16:12:06",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1450",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2066",
          "coagulationStartTime": "2376",
          "coagulationStopTime": "4605",
          "cuttingStartTime": "4696",
          "cuttingRPM": "7",
          "intermittentCuttingStartTime": "4696",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5090",
          "cookingStopTime": "7348",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8756",
          "emptyingStopTime": "10762",
          "residenceTimeInVat": "6754",
          "totalProcesstimeInVat": "10762",
          "coagulationDuration": "2229",
          "cookingDuration": "2258",
          "emptyingDuration": "2006",
          "vatpH": "6.35"
      },
      {
          "vatNo": "3",
          "sequenceNo": "24",
          "recipeNo": "3",
          "DateTime": "2025-04-21 16:45:42",
          "fillingStopTime": "2012",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1448",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2060",
          "coagulationStartTime": "2376",
          "coagulationStopTime": "4363",
          "cuttingStartTime": "4416",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4603",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4989",
          "cookingStopTime": "7058",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8451",
          "emptyingStopTime": "11167",
          "residenceTimeInVat": "6439",
          "totalProcesstimeInVat": "11167",
          "coagulationDuration": "1987",
          "cookingDuration": "2069",
          "emptyingDuration": "2716",
          "vatpH": "6.34"
      },
      {
          "vatNo": "4",
          "sequenceNo": "25",
          "recipeNo": "3",
          "DateTime": "2025-04-21 17:19:14",
          "fillingStopTime": "1986",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1451",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2048",
          "coagulationStartTime": "2369",
          "coagulationStopTime": "4471",
          "cuttingStartTime": "4673",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4854",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5245",
          "cookingStopTime": "7450",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8907",
          "emptyingStopTime": "10871",
          "residenceTimeInVat": "6921",
          "totalProcesstimeInVat": "10871",
          "coagulationDuration": "2102",
          "cookingDuration": "2205",
          "emptyingDuration": "1964",
          "vatpH": "6.3"
      },
      {
          "vatNo": "6",
          "sequenceNo": "27",
          "recipeNo": "3",
          "DateTime": "2025-04-21 18:28:54",
          "fillingStopTime": "1997",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "35.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1288",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2062",
          "coagulationStartTime": "2371",
          "coagulationStopTime": "4449",
          "cuttingStartTime": "4516",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4694",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5089",
          "cookingStopTime": "7194",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "9156",
          "emptyingStopTime": "11226",
          "residenceTimeInVat": "7159",
          "totalProcesstimeInVat": "11226",
          "coagulationDuration": "2078",
          "cookingDuration": "2105",
          "emptyingDuration": "2070",
          "vatpH": "6.34"
      },
      {
          "vatNo": "1",
          "sequenceNo": "29",
          "recipeNo": "4",
          "DateTime": "2025-04-21 19:38:15",
          "fillingStopTime": "1989",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "34.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1811",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2038",
          "coagulationStartTime": "2361",
          "coagulationStopTime": "4437",
          "cuttingStartTime": "4496",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4676",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5070",
          "cookingStopTime": "7183",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8559",
          "emptyingStopTime": "11136",
          "residenceTimeInVat": "6570",
          "totalProcesstimeInVat": "11136",
          "coagulationDuration": "2076",
          "cookingDuration": "2113",
          "emptyingDuration": "2577",
          "vatpH": "6.38"
      },
      {
          "vatNo": "2",
          "sequenceNo": "30",
          "recipeNo": "4",
          "DateTime": "2025-04-21 20:11:24",
          "fillingStopTime": "1997",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "34.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1825",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2045",
          "coagulationStartTime": "2358",
          "coagulationStopTime": "4423",
          "cuttingStartTime": "4632",
          "cuttingRPM": "5",
          "intermittentCuttingStartTime": "4676",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5072",
          "cookingStopTime": "7730",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8899",
          "emptyingStopTime": "10826",
          "residenceTimeInVat": "6902",
          "totalProcesstimeInVat": "10826",
          "coagulationDuration": "2065",
          "cookingDuration": "2658",
          "emptyingDuration": "1927",
          "vatpH": "6.32"
      },
      {
          "vatNo": "3",
          "sequenceNo": "31",
          "recipeNo": "4",
          "DateTime": "2025-04-21 20:44:41",
          "fillingStopTime": "2013",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1820",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2061",
          "coagulationStartTime": "2375",
          "coagulationStopTime": "4478",
          "cuttingStartTime": "4628",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4804",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5198",
          "cookingStopTime": "7259",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "8609",
          "emptyingStopTime": "10723",
          "residenceTimeInVat": "6596",
          "totalProcesstimeInVat": "10723",
          "coagulationDuration": "2103",
          "cookingDuration": "2061",
          "emptyingDuration": "2114",
          "vatpH": "6.4"
      },
      {
          "vatNo": "4",
          "sequenceNo": "32",
          "recipeNo": "4",
          "DateTime": "2025-04-21 21:18:14",
          "fillingStopTime": "1995",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1818",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2055",
          "coagulationStartTime": "2386",
          "coagulationStopTime": "4459",
          "cuttingStartTime": "4616",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4792",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5186",
          "cookingStopTime": "7308",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8507",
          "emptyingStopTime": "10636",
          "residenceTimeInVat": "6512",
          "totalProcesstimeInVat": "10636",
          "coagulationDuration": "2073",
          "cookingDuration": "2122",
          "emptyingDuration": "2129",
          "vatpH": "6.39"
      },
      {
          "vatNo": "6",
          "sequenceNo": "34",
          "recipeNo": "4",
          "DateTime": "2025-04-21 22:24:57",
          "fillingStopTime": "1992",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1807",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2054",
          "coagulationStartTime": "2360",
          "coagulationStopTime": "4442",
          "cuttingStartTime": "4505",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4685",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5079",
          "cookingStopTime": "7151",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8302",
          "emptyingStopTime": "10441",
          "residenceTimeInVat": "6310",
          "totalProcesstimeInVat": "10441",
          "coagulationDuration": "2082",
          "cookingDuration": "2072",
          "emptyingDuration": "2139",
          "vatpH": "6.38"
      },
      {
          "vatNo": "7",
          "sequenceNo": "35",
          "recipeNo": "4",
          "DateTime": "2025-04-21 22:58:25",
          "fillingStopTime": "1978",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1828",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2028",
          "coagulationStartTime": "2343",
          "coagulationStopTime": "4421",
          "cuttingStartTime": "4484",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4663",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5058",
          "cookingStopTime": "7118",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8229",
          "emptyingStopTime": "10116",
          "residenceTimeInVat": "6251",
          "totalProcesstimeInVat": "10116",
          "coagulationDuration": "2078",
          "cookingDuration": "2060",
          "emptyingDuration": "1887",
          "vatpH": "6.37"
      },
      {
          "vatNo": "1",
          "sequenceNo": "1",
          "recipeNo": "1",
          "DateTime": "2025-04-22 06:33:34",
          "fillingStopTime": "1978",
          "fillingRPM": "5",
          "milkQuantityInVat": "11486",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "300",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2627",
          "coagulationStartTime": "2947",
          "coagulationStopTime": "5008",
          "cuttingStartTime": "5070",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5250",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5644",
          "cookingStopTime": "7721",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "10860",
          "emptyingStopTime": "12287",
          "residenceTimeInVat": "8882",
          "totalProcesstimeInVat": "12287",
          "coagulationDuration": "2061",
          "cookingDuration": "2077",
          "emptyingDuration": "1427",
          "vatpH": "6.37"
      },
      {
          "vatNo": "2",
          "sequenceNo": "2",
          "recipeNo": "1",
          "DateTime": "2025-04-22 07:06:33",
          "fillingStopTime": "2011",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "300",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2609",
          "coagulationStartTime": "3002",
          "coagulationStopTime": "5074",
          "cuttingStartTime": "5137",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5317",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5711",
          "cookingStopTime": "7954",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "10308",
          "emptyingStopTime": "12143",
          "residenceTimeInVat": "8297",
          "totalProcesstimeInVat": "12143",
          "coagulationDuration": "2072",
          "cookingDuration": "2243",
          "emptyingDuration": "1835",
          "vatpH": "6.38"
      },
      {
          "vatNo": "3",
          "sequenceNo": "3",
          "recipeNo": "1",
          "DateTime": "2025-04-22 07:40:05",
          "fillingStopTime": "2012",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "303",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2660",
          "coagulationStartTime": "2991",
          "coagulationStopTime": "5065",
          "cuttingStartTime": "5131",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5308",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5702",
          "cookingStopTime": "7891",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "10131",
          "emptyingStopTime": "11922",
          "residenceTimeInVat": "8119",
          "totalProcesstimeInVat": "11922",
          "coagulationDuration": "2074",
          "cookingDuration": "2189",
          "emptyingDuration": "1791",
          "vatpH": "6.37"
      },
      {
          "vatNo": "4",
          "sequenceNo": "4",
          "recipeNo": "1",
          "DateTime": "2025-04-22 08:13:38",
          "fillingStopTime": "2003",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "310",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2667",
          "coagulationStartTime": "3036",
          "coagulationStopTime": "5126",
          "cuttingStartTime": "5193",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5369",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5762",
          "cookingStopTime": "7823",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9708",
          "emptyingStopTime": "11872",
          "residenceTimeInVat": "7705",
          "totalProcesstimeInVat": "11872",
          "coagulationDuration": "2090",
          "cookingDuration": "2061",
          "emptyingDuration": "2164",
          "vatpH": "6.41"
      },
      {
          "vatNo": "5",
          "sequenceNo": "5",
          "recipeNo": "1",
          "DateTime": "2025-04-22 08:47:20",
          "fillingStopTime": "1981",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "31.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "282",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2578",
          "coagulationStartTime": "2905",
          "coagulationStopTime": "4983",
          "cuttingStartTime": "5048",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5226",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5620",
          "cookingStopTime": "7686",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9687",
          "emptyingStopTime": "11783",
          "residenceTimeInVat": "7706",
          "totalProcesstimeInVat": "11783",
          "coagulationDuration": "2078",
          "cookingDuration": "2066",
          "emptyingDuration": "2096",
          "vatpH": "6.43"
      },
      {
          "vatNo": "6",
          "sequenceNo": "6",
          "recipeNo": "1",
          "DateTime": "2025-04-22 09:20:22",
          "fillingStopTime": "2003",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "304",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2600",
          "coagulationStartTime": "2963",
          "coagulationStopTime": "5048",
          "cuttingStartTime": "5133",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5308",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5704",
          "cookingStopTime": "7772",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9635",
          "emptyingStopTime": "11878",
          "residenceTimeInVat": "7632",
          "totalProcesstimeInVat": "11878",
          "coagulationDuration": "2085",
          "cookingDuration": "2068",
          "emptyingDuration": "2243",
          "vatpH": "6.42"
      },
      {
          "vatNo": "7",
          "sequenceNo": "7",
          "recipeNo": "1",
          "DateTime": "2025-04-22 09:53:46",
          "fillingStopTime": "2110",
          "fillingRPM": "5",
          "milkQuantityInVat": "11308",
          "temperatureOfMilkInVat": "32.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "309",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2608",
          "coagulationStartTime": "2935",
          "coagulationStopTime": "5026",
          "cuttingStartTime": "5088",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5268",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5701",
          "cookingStopTime": "7762",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9650",
          "emptyingStopTime": "12323",
          "residenceTimeInVat": "7540",
          "totalProcesstimeInVat": "12323",
          "coagulationDuration": "2091",
          "cookingDuration": "2061",
          "emptyingDuration": "2673",
          "vatpH": "6.45"
      },
      {
          "vatNo": "1",
          "sequenceNo": "8",
          "recipeNo": "1",
          "DateTime": "2025-04-22 10:28:56",
          "fillingStopTime": "2447",
          "fillingRPM": "5",
          "milkQuantityInVat": "11486",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "201",
          "rennetQuantity": "200",
          "rennetAdditionTime": "3043",
          "coagulationStartTime": "3370",
          "coagulationStopTime": "5436",
          "cuttingStartTime": "5499",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5678",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "6072",
          "cookingStopTime": "8133",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "10010",
          "emptyingStopTime": "12454",
          "residenceTimeInVat": "7563",
          "totalProcesstimeInVat": "12454",
          "coagulationDuration": "2066",
          "cookingDuration": "2061",
          "emptyingDuration": "2444",
          "vatpH": "6.46"
      },
      {
          "vatNo": "2",
          "sequenceNo": "9",
          "recipeNo": "1",
          "DateTime": "2025-04-22 11:09:44",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32",
          "cultureQuantity": "200",
          "cultureAdditionTime": "302",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2599",
          "coagulationStartTime": "2955",
          "coagulationStopTime": "5040",
          "cuttingStartTime": "5102",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5282",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5676",
          "cookingStopTime": "7914",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9785",
          "emptyingStopTime": "11947",
          "residenceTimeInVat": "7783",
          "totalProcesstimeInVat": "11947",
          "coagulationDuration": "2085",
          "cookingDuration": "2238",
          "emptyingDuration": "2162",
          "vatpH": "6.42"
      },
      {
          "vatNo": "4",
          "sequenceNo": "11",
          "recipeNo": "1",
          "DateTime": "2025-04-22 12:16:52",
          "fillingStopTime": "1985",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2636",
          "coagulationStartTime": "3004",
          "coagulationStopTime": "5082",
          "cuttingStartTime": "5145",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5325",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5719",
          "cookingStopTime": "7919",
          "cookingRPM": "5",
          "cookingTemperature": "38.5",
          "emptyingStartTime": "9657",
          "emptyingStopTime": "11743",
          "residenceTimeInVat": "7672",
          "totalProcesstimeInVat": "11743",
          "coagulationDuration": "2078",
          "cookingDuration": "2200",
          "emptyingDuration": "2086",
          "vatpH": "6.44"
      },
      {
          "vatNo": "5",
          "sequenceNo": "12",
          "recipeNo": "1",
          "DateTime": "2025-04-22 12:50:15",
          "fillingStopTime": "1984",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "31.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "288",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2579",
          "coagulationStartTime": "2899",
          "coagulationStopTime": "4959",
          "cuttingStartTime": "5022",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5202",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5596",
          "cookingStopTime": "7657",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9536",
          "emptyingStopTime": "11752",
          "residenceTimeInVat": "7552",
          "totalProcesstimeInVat": "11752",
          "coagulationDuration": "2060",
          "cookingDuration": "2061",
          "emptyingDuration": "2216",
          "vatpH": "6.43"
      },
      {
          "vatNo": "6",
          "sequenceNo": "13",
          "recipeNo": "1",
          "DateTime": "2025-04-22 13:23:33",
          "fillingStopTime": "1994",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "311",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2584",
          "coagulationStartTime": "2904",
          "coagulationStopTime": "4990",
          "cuttingStartTime": "5052",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5232",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5626",
          "cookingStopTime": "7686",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9565",
          "emptyingStopTime": "11831",
          "residenceTimeInVat": "7571",
          "totalProcesstimeInVat": "11831",
          "coagulationDuration": "2086",
          "cookingDuration": "2060",
          "emptyingDuration": "2266",
          "vatpH": "6.45"
      },
      {
          "vatNo": "7",
          "sequenceNo": "14",
          "recipeNo": "1",
          "DateTime": "2025-04-22 13:56:47",
          "fillingStopTime": "2000",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "299",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2597",
          "coagulationStartTime": "2954",
          "coagulationStopTime": "5035",
          "cuttingStartTime": "5159",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5335",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5729",
          "cookingStopTime": "7799",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9655",
          "emptyingStopTime": "11553",
          "residenceTimeInVat": "7655",
          "totalProcesstimeInVat": "11553",
          "coagulationDuration": "2081",
          "cookingDuration": "2070",
          "emptyingDuration": "1898",
          "vatpH": "6.41"
      },
      {
          "vatNo": "1",
          "sequenceNo": "15",
          "recipeNo": "2",
          "DateTime": "2025-04-22 14:32:53",
          "fillingStopTime": "2003",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "909",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2054",
          "coagulationStartTime": "2441",
          "coagulationStopTime": "4526",
          "cuttingStartTime": "4588",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4767",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5162",
          "cookingStopTime": "7222",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9202",
          "emptyingStopTime": "11086",
          "residenceTimeInVat": "7199",
          "totalProcesstimeInVat": "11086",
          "coagulationDuration": "2085",
          "cookingDuration": "2060",
          "emptyingDuration": "1884",
          "vatpH": "6.43"
      },
      {
          "vatNo": "2",
          "sequenceNo": "16",
          "recipeNo": "2",
          "DateTime": "2025-04-22 15:06:30",
          "fillingStopTime": "1992",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "896",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2046",
          "coagulationStartTime": "2421",
          "coagulationStopTime": "4482",
          "cuttingStartTime": "4544",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4727",
          "intermittentCuttingRPM": "2",
          "cookingStartTime": "5088",
          "cookingStopTime": "7252",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8927",
          "emptyingStopTime": "10965",
          "residenceTimeInVat": "6935",
          "totalProcesstimeInVat": "10965",
          "coagulationDuration": "2061",
          "cookingDuration": "2164",
          "emptyingDuration": "2038",
          "vatpH": "6.43"
      },
      {
          "vatNo": "4",
          "sequenceNo": "18",
          "recipeNo": "1",
          "DateTime": "2025-04-22 16:13:14",
          "fillingStopTime": "2097",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "301",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2695",
          "coagulationStartTime": "3058",
          "coagulationStopTime": "5124",
          "cuttingStartTime": "5187",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5367",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5760",
          "cookingStopTime": "7909",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9282",
          "emptyingStopTime": "11144",
          "residenceTimeInVat": "7185",
          "totalProcesstimeInVat": "11144",
          "coagulationDuration": "2066",
          "cookingDuration": "2149",
          "emptyingDuration": "1862",
          "vatpH": "6.44"
      },
      {
          "vatNo": "5",
          "sequenceNo": "19",
          "recipeNo": "2",
          "DateTime": "2025-04-22 16:48:11",
          "fillingStopTime": "2002",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "909",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2054",
          "coagulationStartTime": "2444",
          "coagulationStopTime": "4447",
          "cuttingStartTime": "4600",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4774",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5168",
          "cookingStopTime": "7236",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8887",
          "emptyingStopTime": "11023",
          "residenceTimeInVat": "6885",
          "totalProcesstimeInVat": "11023",
          "coagulationDuration": "2003",
          "cookingDuration": "2068",
          "emptyingDuration": "2136",
          "vatpH": "6.43"
      },
      {
          "vatNo": "6",
          "sequenceNo": "20",
          "recipeNo": "2",
          "DateTime": "2025-04-22 17:21:47",
          "fillingStopTime": "1989",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "895",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2051",
          "coagulationStartTime": "2367",
          "coagulationStopTime": "4457",
          "cuttingStartTime": "4520",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4700",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5094",
          "cookingStopTime": "7202",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8847",
          "emptyingStopTime": "11224",
          "residenceTimeInVat": "6858",
          "totalProcesstimeInVat": "11224",
          "coagulationDuration": "2090",
          "cookingDuration": "2108",
          "emptyingDuration": "2377",
          "vatpH": "6.43"
      },
      {
          "vatNo": "7",
          "sequenceNo": "21",
          "recipeNo": "2",
          "DateTime": "2025-04-22 17:55:14",
          "fillingStopTime": "2019",
          "fillingRPM": "5",
          "milkQuantityInVat": "11263",
          "temperatureOfMilkInVat": "35",
          "cultureQuantity": "200",
          "cultureAdditionTime": "888",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2065",
          "coagulationStartTime": "2378",
          "coagulationStopTime": "4445",
          "cuttingStartTime": "4520",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4687",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5081",
          "cookingStopTime": "7360",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8990",
          "emptyingStopTime": "10918",
          "residenceTimeInVat": "6971",
          "totalProcesstimeInVat": "10918",
          "coagulationDuration": "2067",
          "cookingDuration": "2279",
          "emptyingDuration": "1928",
          "vatpH": "6.4"
      },
      {
          "vatNo": "1",
          "sequenceNo": "22",
          "recipeNo": "3",
          "DateTime": "2025-04-22 18:28:53",
          "fillingStopTime": "1970",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1419",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2019",
          "coagulationStartTime": "2334",
          "coagulationStopTime": "4424",
          "cuttingStartTime": "4487",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4666",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5061",
          "cookingStopTime": "7198",
          "cookingRPM": "5",
          "cookingTemperature": "38.8",
          "emptyingStartTime": "8682",
          "emptyingStopTime": "10959",
          "residenceTimeInVat": "6712",
          "totalProcesstimeInVat": "10959",
          "coagulationDuration": "2090",
          "cookingDuration": "2137",
          "emptyingDuration": "2277",
          "vatpH": "6.42"
      },
      {
          "vatNo": "2",
          "sequenceNo": "23",
          "recipeNo": "3",
          "DateTime": "2025-04-22 19:02:00",
          "fillingStopTime": "1987",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1436",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2042",
          "coagulationStartTime": "2356",
          "coagulationStopTime": "4435",
          "cuttingStartTime": "4498",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4678",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5211",
          "cookingStopTime": "7353",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8727",
          "emptyingStopTime": "10959",
          "residenceTimeInVat": "6740",
          "totalProcesstimeInVat": "10959",
          "coagulationDuration": "2079",
          "cookingDuration": "2142",
          "emptyingDuration": "2232",
          "vatpH": "6.43"
      },
      {
          "vatNo": "3",
          "sequenceNo": "24",
          "recipeNo": "2",
          "DateTime": "2025-04-22 19:35:23",
          "fillingStopTime": "2003",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "904",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2054",
          "coagulationStartTime": "2368",
          "coagulationStopTime": "4429",
          "cuttingStartTime": "4491",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4671",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5065",
          "cookingStopTime": "7126",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8742",
          "emptyingStopTime": "11172",
          "residenceTimeInVat": "6739",
          "totalProcesstimeInVat": "11172",
          "coagulationDuration": "2061",
          "cookingDuration": "2061",
          "emptyingDuration": "2430",
          "vatpH": "6.4"
      },
      {
          "vatNo": "4",
          "sequenceNo": "25",
          "recipeNo": "2",
          "DateTime": "2025-04-22 20:08:46",
          "fillingStopTime": "1996",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "907",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2047",
          "coagulationStartTime": "2361",
          "coagulationStopTime": "4452",
          "cuttingStartTime": "4674",
          "cuttingRPM": "3.5",
          "intermittentCuttingStartTime": "4792",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5186",
          "cookingStopTime": "7352",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8972",
          "emptyingStopTime": "10976",
          "residenceTimeInVat": "6976",
          "totalProcesstimeInVat": "10976",
          "coagulationDuration": "2091",
          "cookingDuration": "2166",
          "emptyingDuration": "2004",
          "vatpH": "6.4"
      },
      {
          "vatNo": "5",
          "sequenceNo": "26",
          "recipeNo": "2",
          "DateTime": "2025-04-22 20:42:17",
          "fillingStopTime": "1972",
          "fillingRPM": "5",
          "milkQuantityInVat": "10984",
          "temperatureOfMilkInVat": "33.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "893",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2026",
          "coagulationStartTime": "2370",
          "coagulationStopTime": "4443",
          "cuttingStartTime": "4506",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4685",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5080",
          "cookingStopTime": "7149",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8770",
          "emptyingStopTime": "10679",
          "residenceTimeInVat": "6798",
          "totalProcesstimeInVat": "10679",
          "coagulationDuration": "2073",
          "cookingDuration": "2069",
          "emptyingDuration": "1909",
          "vatpH": "6.4"
      },
      {
          "vatNo": "6",
          "sequenceNo": "27",
          "recipeNo": "3",
          "DateTime": "2025-04-22 21:15:10",
          "fillingStopTime": "1996",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1454",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2049",
          "coagulationStartTime": "2369",
          "coagulationStopTime": "4447",
          "cuttingStartTime": "4510",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4690",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5090",
          "cookingStopTime": "7168",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8543",
          "emptyingStopTime": "10727",
          "residenceTimeInVat": "6547",
          "totalProcesstimeInVat": "10727",
          "coagulationDuration": "2078",
          "cookingDuration": "2078",
          "emptyingDuration": "2184",
          "vatpH": "6.41"
      },
      {
          "vatNo": "7",
          "sequenceNo": "28",
          "recipeNo": "3",
          "DateTime": "2025-04-22 21:48:43",
          "fillingStopTime": "1991",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1436",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2043",
          "coagulationStartTime": "2369",
          "coagulationStopTime": "4459",
          "cuttingStartTime": "4535",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4714",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5108",
          "cookingStopTime": "7169",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8543",
          "emptyingStopTime": "10468",
          "residenceTimeInVat": "6552",
          "totalProcesstimeInVat": "10468",
          "coagulationDuration": "2090",
          "cookingDuration": "2061",
          "emptyingDuration": "1925",
          "vatpH": "6.42"
      },
      {
          "vatNo": "1",
          "sequenceNo": "29",
          "recipeNo": "4",
          "DateTime": "2025-04-22 22:22:11",
          "fillingStopTime": "1985",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1802",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2038",
          "coagulationStartTime": "2365",
          "coagulationStopTime": "4426",
          "cuttingStartTime": "4488",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4668",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5062",
          "cookingStopTime": "7122",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8267",
          "emptyingStopTime": "10636",
          "residenceTimeInVat": "6282",
          "totalProcesstimeInVat": "10636",
          "coagulationDuration": "2061",
          "cookingDuration": "2060",
          "emptyingDuration": "2369",
          "vatpH": "6.45"
      },
      {
          "vatNo": "3",
          "sequenceNo": "31",
          "recipeNo": "4",
          "DateTime": "2025-04-22 23:28:48",
          "fillingStopTime": "2007",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1812",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2060",
          "coagulationStartTime": "2387",
          "coagulationStopTime": "4459",
          "cuttingStartTime": "4583",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4762",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5156",
          "cookingStopTime": "7216",
          "cookingRPM": "5",
          "cookingTemperature": "39.3",
          "emptyingStartTime": "8323",
          "emptyingStopTime": "10567",
          "residenceTimeInVat": "6316",
          "totalProcesstimeInVat": "10567",
          "coagulationDuration": "2072",
          "cookingDuration": "2060",
          "emptyingDuration": "2244",
          "vatpH": "6.42"
      },
      {
          "vatNo": "4",
          "sequenceNo": "32",
          "recipeNo": "4",
          "DateTime": "2025-04-23 00:02:30",
          "fillingStopTime": "1983",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1804",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2041",
          "coagulationStartTime": "2349",
          "coagulationStopTime": "4415",
          "cuttingStartTime": "4478",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4668",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5052",
          "cookingStopTime": "7259",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8354",
          "emptyingStopTime": "10472",
          "residenceTimeInVat": "6371",
          "totalProcesstimeInVat": "10472",
          "coagulationDuration": "2066",
          "cookingDuration": "2207",
          "emptyingDuration": "2118",
          "vatpH": "6.4"
      },
      {
          "vatNo": "5",
          "sequenceNo": "33",
          "recipeNo": "4",
          "DateTime": "2025-04-23 00:35:48",
          "fillingStopTime": "1995",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "33.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1803",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2039",
          "coagulationStartTime": "2360",
          "coagulationStopTime": "4432",
          "cuttingStartTime": "4495",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4675",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5069",
          "cookingStopTime": "7129",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8280",
          "emptyingStopTime": "10809",
          "residenceTimeInVat": "6285",
          "totalProcesstimeInVat": "10809",
          "coagulationDuration": "2072",
          "cookingDuration": "2060",
          "emptyingDuration": "2529",
          "vatpH": "6.4"
      },
      {
          "vatNo": "6",
          "sequenceNo": "34",
          "recipeNo": "4",
          "DateTime": "2025-04-23 01:09:03",
          "fillingStopTime": "1993",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1809",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2045",
          "coagulationStartTime": "2372",
          "coagulationStopTime": "4462",
          "cuttingStartTime": "4526",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4704",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5099",
          "cookingStopTime": "7168",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8385",
          "emptyingStopTime": "10713",
          "residenceTimeInVat": "6392",
          "totalProcesstimeInVat": "10713",
          "coagulationDuration": "2090",
          "cookingDuration": "2069",
          "emptyingDuration": "2328",
          "vatpH": "6.41"
      },
      {
          "vatNo": "7",
          "sequenceNo": "35",
          "recipeNo": "4",
          "DateTime": "2025-04-23 01:42:30",
          "fillingStopTime": "2123",
          "fillingRPM": "5",
          "milkQuantityInVat": "10790",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1803",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2173",
          "coagulationStartTime": "2504",
          "coagulationStopTime": "4584",
          "cuttingStartTime": "4679",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4856",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5250",
          "cookingStopTime": "7310",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8419",
          "emptyingStopTime": "10433",
          "residenceTimeInVat": "6296",
          "totalProcesstimeInVat": "10433",
          "coagulationDuration": "2080",
          "cookingDuration": "2060",
          "emptyingDuration": "2014",
          "vatpH": "6.4"
      },
      {
          "vatNo": "1",
          "sequenceNo": "1",
          "recipeNo": "1",
          "DateTime": "2025-04-23 09:52:22",
          "fillingStopTime": "2129",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "304",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2655",
          "coagulationStartTime": "2982",
          "coagulationStopTime": "5066",
          "cuttingStartTime": "5135",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5315",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5770",
          "cookingStopTime": "7798",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9679",
          "emptyingStopTime": "12072",
          "residenceTimeInVat": "7550",
          "totalProcesstimeInVat": "12072",
          "coagulationDuration": "2084",
          "cookingDuration": "2028",
          "emptyingDuration": "2393",
          "vatpH": "6.5"
      },
      {
          "vatNo": "2",
          "sequenceNo": "2",
          "recipeNo": "1",
          "DateTime": "2025-04-23 10:27:51",
          "fillingStopTime": "1887",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "181",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2532",
          "coagulationStartTime": "2858",
          "coagulationStopTime": "4931",
          "cuttingStartTime": "4994",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5174",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5568",
          "cookingStopTime": "7847",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9683",
          "emptyingStopTime": "11799",
          "residenceTimeInVat": "7796",
          "totalProcesstimeInVat": "11799",
          "coagulationDuration": "2073",
          "cookingDuration": "2279",
          "emptyingDuration": "2116",
          "vatpH": "6.47"
      },
      {
          "vatNo": "3",
          "sequenceNo": "3",
          "recipeNo": "1",
          "DateTime": "2025-04-23 10:59:19",
          "fillingStopTime": "2013",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "299",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2608",
          "coagulationStartTime": "2941",
          "coagulationStopTime": "5013",
          "cuttingStartTime": "5076",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5368",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5662",
          "cookingStopTime": "7856",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9696",
          "emptyingStopTime": "11753",
          "residenceTimeInVat": "7683",
          "totalProcesstimeInVat": "11753",
          "coagulationDuration": "2072",
          "cookingDuration": "2194",
          "emptyingDuration": "2057",
          "vatpH": "6.47"
      },
      {
          "vatNo": "4",
          "sequenceNo": "4",
          "recipeNo": "1",
          "DateTime": "2025-04-23 11:33:20",
          "fillingStopTime": "1975",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "276",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2573",
          "coagulationStartTime": "2899",
          "coagulationStopTime": "4972",
          "cuttingStartTime": "5034",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5214",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5608",
          "cookingStopTime": "7681",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9518",
          "emptyingStopTime": "12158",
          "residenceTimeInVat": "7543",
          "totalProcesstimeInVat": "12158",
          "coagulationDuration": "2073",
          "cookingDuration": "2073",
          "emptyingDuration": "2640",
          "vatpH": "6.44"
      },
      {
          "vatNo": "5",
          "sequenceNo": "5",
          "recipeNo": "1",
          "DateTime": "2025-04-23 12:06:31",
          "fillingStopTime": "1986",
          "fillingRPM": "5",
          "milkQuantityInVat": "10984",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "285",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2624",
          "coagulationStartTime": "2956",
          "coagulationStopTime": "5023",
          "cuttingStartTime": "5085",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5265",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5664",
          "cookingStopTime": "7737",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9580",
          "emptyingStopTime": "11830",
          "residenceTimeInVat": "7594",
          "totalProcesstimeInVat": "11830",
          "coagulationDuration": "2067",
          "cookingDuration": "2073",
          "emptyingDuration": "2250",
          "vatpH": "6.48"
      },
      {
          "vatNo": "6",
          "sequenceNo": "6",
          "recipeNo": "1",
          "DateTime": "2025-04-23 12:39:39",
          "fillingStopTime": "2003",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "31.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "302",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2599",
          "coagulationStartTime": "2926",
          "coagulationStopTime": "4994",
          "cuttingStartTime": "5057",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5237",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5637",
          "cookingStopTime": "7693",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9674",
          "emptyingStopTime": "11812",
          "residenceTimeInVat": "7671",
          "totalProcesstimeInVat": "11812",
          "coagulationDuration": "2068",
          "cookingDuration": "2056",
          "emptyingDuration": "2138",
          "vatpH": "6.49"
      },
      {
          "vatNo": "7",
          "sequenceNo": "7",
          "recipeNo": "1",
          "DateTime": "2025-04-23 13:13:18",
          "fillingStopTime": "1990",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "31.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2587",
          "coagulationStartTime": "2901",
          "coagulationStopTime": "4973",
          "cuttingStartTime": "5072",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5252",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5646",
          "cookingStopTime": "7745",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9578",
          "emptyingStopTime": "11890",
          "residenceTimeInVat": "7588",
          "totalProcesstimeInVat": "11890",
          "coagulationDuration": "2072",
          "cookingDuration": "2099",
          "emptyingDuration": "2312",
          "vatpH": "6.47"
      },
      {
          "vatNo": "1",
          "sequenceNo": "8",
          "recipeNo": "1",
          "DateTime": "2025-04-23 13:46:29",
          "fillingStopTime": "2008",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "32.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "299",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2601",
          "coagulationStartTime": "2994",
          "coagulationStopTime": "5088",
          "cuttingStartTime": "5141",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5321",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5721",
          "cookingStopTime": "7803",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9743",
          "emptyingStopTime": "12072",
          "residenceTimeInVat": "7735",
          "totalProcesstimeInVat": "12072",
          "coagulationDuration": "2094",
          "cookingDuration": "2082",
          "emptyingDuration": "2329",
          "vatpH": "6.41"
      },
      {
          "vatNo": "2",
          "sequenceNo": "9",
          "recipeNo": "1",
          "DateTime": "2025-04-23 14:19:57",
          "fillingStopTime": "2001",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "296",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2601",
          "coagulationStartTime": "2961",
          "coagulationStopTime": "5046",
          "cuttingStartTime": "5108",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5298",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5683",
          "cookingStopTime": "7944",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "9821",
          "emptyingStopTime": "11942",
          "residenceTimeInVat": "7820",
          "totalProcesstimeInVat": "11942",
          "coagulationDuration": "2085",
          "cookingDuration": "2261",
          "emptyingDuration": "2121",
          "vatpH": "6.41"
      },
      {
          "vatNo": "3",
          "sequenceNo": "10",
          "recipeNo": "1",
          "DateTime": "2025-04-23 14:53:34",
          "fillingStopTime": "2123",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "285",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2594",
          "coagulationStartTime": "2915",
          "coagulationStopTime": "4981",
          "cuttingStartTime": "5044",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5223",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5718",
          "cookingStopTime": "7812",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9701",
          "emptyingStopTime": "12013",
          "residenceTimeInVat": "7578",
          "totalProcesstimeInVat": "12013",
          "coagulationDuration": "2066",
          "cookingDuration": "2094",
          "emptyingDuration": "2312",
          "vatpH": "6.46"
      },
      {
          "vatNo": "4",
          "sequenceNo": "11",
          "recipeNo": "1",
          "DateTime": "2025-04-23 15:29:52",
          "fillingStopTime": "1972",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "270",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2567",
          "coagulationStartTime": "2899",
          "coagulationStopTime": "4966",
          "cuttingStartTime": "5029",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5208",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5602",
          "cookingStopTime": "7762",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "9644",
          "emptyingStopTime": "11772",
          "residenceTimeInVat": "7672",
          "totalProcesstimeInVat": "11772",
          "coagulationDuration": "2067",
          "cookingDuration": "2160",
          "emptyingDuration": "2128",
          "vatpH": "6.47"
      },
      {
          "vatNo": "5",
          "sequenceNo": "12",
          "recipeNo": "1",
          "DateTime": "2025-04-23 16:02:59",
          "fillingStopTime": "1990",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "32.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "289",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2586",
          "coagulationStartTime": "2912",
          "coagulationStopTime": "5157",
          "cuttingStartTime": "5385",
          "cuttingRPM": "7",
          "intermittentCuttingStartTime": "5384",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5628",
          "cookingStopTime": "7689",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9578",
          "emptyingStopTime": "12442",
          "residenceTimeInVat": "7588",
          "totalProcesstimeInVat": "12442",
          "coagulationDuration": "2245",
          "cookingDuration": "2061",
          "emptyingDuration": "2864",
          "vatpH": "6.48"
      },
      {
          "vatNo": "6",
          "sequenceNo": "13",
          "recipeNo": "1",
          "DateTime": "2025-04-23 16:36:25",
          "fillingStopTime": "1996",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "285",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2582",
          "coagulationStartTime": "2915",
          "coagulationStopTime": "4984",
          "cuttingStartTime": "5047",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5227",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5620",
          "cookingStopTime": "7728",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "10337",
          "emptyingStopTime": "12203",
          "residenceTimeInVat": "8341",
          "totalProcesstimeInVat": "12203",
          "coagulationDuration": "2069",
          "cookingDuration": "2108",
          "emptyingDuration": "1866",
          "vatpH": "6.42"
      },
      {
          "vatNo": "7",
          "sequenceNo": "14",
          "recipeNo": "1",
          "DateTime": "2025-04-23 17:10:39",
          "fillingStopTime": "2146",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "34",
          "cultureQuantity": "200",
          "cultureAdditionTime": "296",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2746",
          "coagulationStartTime": "3078",
          "coagulationStopTime": "5149",
          "cuttingStartTime": "5230",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5404",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5798",
          "cookingStopTime": "8063",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9950",
          "emptyingStopTime": "11897",
          "residenceTimeInVat": "7804",
          "totalProcesstimeInVat": "11897",
          "coagulationDuration": "2071",
          "cookingDuration": "2265",
          "emptyingDuration": "1947",
          "vatpH": "6.4"
      },
      {
          "vatNo": "1",
          "sequenceNo": "15",
          "recipeNo": "2",
          "DateTime": "2025-04-23 17:46:54",
          "fillingStopTime": "1974",
          "fillingRPM": "5",
          "milkQuantityInVat": "11529",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "880",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2024",
          "coagulationStartTime": "2515",
          "coagulationStopTime": "4423",
          "cuttingStartTime": "4657",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4836",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5230",
          "cookingStopTime": "7290",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9485",
          "emptyingStopTime": "11416",
          "residenceTimeInVat": "7511",
          "totalProcesstimeInVat": "11416",
          "coagulationDuration": "1908",
          "cookingDuration": "2060",
          "emptyingDuration": "1931",
          "vatpH": "6.39"
      },
      {
          "vatNo": "2",
          "sequenceNo": "16",
          "recipeNo": "2",
          "DateTime": "2025-04-23 18:20:03",
          "fillingStopTime": "1993",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "895",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2046",
          "coagulationStartTime": "2379",
          "coagulationStopTime": "4452",
          "cuttingStartTime": "4514",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4694",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5058",
          "cookingStopTime": "7257",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9206",
          "emptyingStopTime": "11319",
          "residenceTimeInVat": "7213",
          "totalProcesstimeInVat": "11319",
          "coagulationDuration": "2073",
          "cookingDuration": "2199",
          "emptyingDuration": "2113",
          "vatpH": "6.38"
      },
      {
          "vatNo": "3",
          "sequenceNo": "17",
          "recipeNo": "2",
          "DateTime": "2025-04-23 18:53:31",
          "fillingStopTime": "2111",
          "fillingRPM": "4",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "31.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "894",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2111",
          "coagulationStartTime": "2366",
          "coagulationStopTime": "4433",
          "cuttingStartTime": "4495",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4675",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5069",
          "cookingStopTime": "7249",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "8911",
          "emptyingStopTime": "11096",
          "residenceTimeInVat": "6800",
          "totalProcesstimeInVat": "11096",
          "coagulationDuration": "2067",
          "cookingDuration": "2180",
          "emptyingDuration": "2185",
          "vatpH": "6.43"
      },
      {
          "vatNo": "4",
          "sequenceNo": "18",
          "recipeNo": "2",
          "DateTime": "2025-04-23 19:28:42",
          "fillingStopTime": "1899",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "794",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1946",
          "coagulationStartTime": "2278",
          "coagulationStopTime": "4351",
          "cuttingStartTime": "4413",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4593",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4987",
          "cookingStopTime": "7200",
          "cookingRPM": "5",
          "cookingTemperature": "38.6",
          "emptyingStartTime": "8763",
          "emptyingStopTime": "14962",
          "residenceTimeInVat": "6864",
          "totalProcesstimeInVat": "14962",
          "coagulationDuration": "2073",
          "cookingDuration": "2213",
          "emptyingDuration": "6199",
          "vatpH": "6.42"
      },
      {
          "vatNo": "5",
          "sequenceNo": "19",
          "recipeNo": "2",
          "DateTime": "2025-04-23 20:02:16",
          "fillingStopTime": "2000",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "901",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2053",
          "coagulationStartTime": "2367",
          "coagulationStopTime": "4451",
          "cuttingStartTime": "4514",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4693",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5186",
          "cookingStopTime": "7148",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "8793",
          "emptyingStopTime": "12948",
          "residenceTimeInVat": "6793",
          "totalProcesstimeInVat": "12948",
          "coagulationDuration": "2084",
          "cookingDuration": "1962",
          "emptyingDuration": "4155",
          "vatpH": "6.41"
      },
      {
          "vatNo": "5",
          "sequenceNo": "26",
          "recipeNo": "3",
          "DateTime": "2025-04-23 23:59:59",
          "fillingStopTime": "1964",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1412",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2111",
          "coagulationStartTime": "2351",
          "coagulationStopTime": "4423",
          "cuttingStartTime": "4497",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4666",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5060",
          "cookingStopTime": "7120",
          "cookingRPM": "5",
          "cookingTemperature": "39.2",
          "emptyingStartTime": "9112",
          "emptyingStopTime": "11711",
          "residenceTimeInVat": "7148",
          "totalProcesstimeInVat": "11711",
          "coagulationDuration": "2072",
          "cookingDuration": "2060",
          "emptyingDuration": "2599",
          "vatpH": "6.48"
      },
      {
          "vatNo": "6",
          "sequenceNo": "27",
          "recipeNo": "3",
          "DateTime": "2025-04-24 00:33:32",
          "fillingStopTime": "1954",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1406",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2007",
          "coagulationStartTime": "2321",
          "coagulationStopTime": "4401",
          "cuttingStartTime": "4464",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4643",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5038",
          "cookingStopTime": "7100",
          "cookingRPM": "5",
          "cookingTemperature": "39.3",
          "emptyingStartTime": "9268",
          "emptyingStopTime": "11325",
          "residenceTimeInVat": "7314",
          "totalProcesstimeInVat": "11325",
          "coagulationDuration": "2080",
          "cookingDuration": "2062",
          "emptyingDuration": "2057",
          "vatpH": "6.46"
      },
      {
          "vatNo": "7",
          "sequenceNo": "28",
          "recipeNo": "3",
          "DateTime": "2025-04-24 01:06:28",
          "fillingStopTime": "1988",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1431",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2043",
          "coagulationStartTime": "2363",
          "coagulationStopTime": "4429",
          "cuttingStartTime": "4492",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4672",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5066",
          "cookingStopTime": "7127",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9004",
          "emptyingStopTime": "11090",
          "residenceTimeInVat": "7016",
          "totalProcesstimeInVat": "11090",
          "coagulationDuration": "2066",
          "cookingDuration": "2061",
          "emptyingDuration": "2086",
          "vatpH": "6.47"
      },
      {
          "vatNo": "1",
          "sequenceNo": "29",
          "recipeNo": "4",
          "DateTime": "2025-04-24 01:39:37",
          "fillingStopTime": "2008",
          "fillingRPM": "5",
          "milkQuantityInVat": "11572",
          "temperatureOfMilkInVat": "32.1",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1817",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2054",
          "coagulationStartTime": "2374",
          "coagulationStopTime": "4453",
          "cuttingStartTime": "4516",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4695",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5089",
          "cookingStopTime": "7154",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8725",
          "emptyingStopTime": "10856",
          "residenceTimeInVat": "6717",
          "totalProcesstimeInVat": "10856",
          "coagulationDuration": "2079",
          "cookingDuration": "2065",
          "emptyingDuration": "2131",
          "vatpH": "6.48"
      },
      {
          "vatNo": "2",
          "sequenceNo": "30",
          "recipeNo": "3",
          "DateTime": "2025-04-24 02:13:05",
          "fillingStopTime": "2001",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "33",
          "cultureQuantity": "200",
          "cultureAdditionTime": "1451",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2050",
          "coagulationStartTime": "2365",
          "coagulationStopTime": "4427",
          "cuttingStartTime": "4488",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4668",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5062",
          "cookingStopTime": "7198",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8546",
          "emptyingStopTime": "11008",
          "residenceTimeInVat": "6545",
          "totalProcesstimeInVat": "11008",
          "coagulationDuration": "2062",
          "cookingDuration": "2136",
          "emptyingDuration": "2462",
          "vatpH": "6.47"
      },
      {
          "vatNo": "3",
          "sequenceNo": "31",
          "recipeNo": "2",
          "DateTime": "2025-04-24 02:46:43",
          "fillingStopTime": "1997",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.8",
          "cultureQuantity": "200",
          "cultureAdditionTime": "890",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2047",
          "coagulationStartTime": "2362",
          "coagulationStopTime": "4434",
          "cuttingStartTime": "4497",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4676",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5071",
          "cookingStopTime": "7131",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8724",
          "emptyingStopTime": "11244",
          "residenceTimeInVat": "6727",
          "totalProcesstimeInVat": "11244",
          "coagulationDuration": "2072",
          "cookingDuration": "2060",
          "emptyingDuration": "2520",
          "vatpH": "6.46"
      },
      {
          "vatNo": "4",
          "sequenceNo": "32",
          "recipeNo": "2",
          "DateTime": "2025-04-24 03:20:28",
          "fillingStopTime": "1966",
          "fillingRPM": "5",
          "milkQuantityInVat": "10936",
          "temperatureOfMilkInVat": "32.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "881",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2020",
          "coagulationStartTime": "2340",
          "coagulationStopTime": "4419",
          "cuttingStartTime": "4482",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4661",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5055",
          "cookingStopTime": "7175",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "8763",
          "emptyingStopTime": "11067",
          "residenceTimeInVat": "6797",
          "totalProcesstimeInVat": "11067",
          "coagulationDuration": "2079",
          "cookingDuration": "2120",
          "emptyingDuration": "2304",
          "vatpH": "6.45"
      },
      {
          "vatNo": "5",
          "sequenceNo": "33",
          "recipeNo": "2",
          "DateTime": "2025-04-24 03:53:53",
          "fillingStopTime": "2098",
          "fillingRPM": "4",
          "milkQuantityInVat": "11263",
          "temperatureOfMilkInVat": "33.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "878",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2098",
          "coagulationStartTime": "2347",
          "coagulationStopTime": "4425",
          "cuttingStartTime": "4488",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4667",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5062",
          "cookingStopTime": "7138",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8726",
          "emptyingStopTime": "11246",
          "residenceTimeInVat": "6628",
          "totalProcesstimeInVat": "11246",
          "coagulationDuration": "2078",
          "cookingDuration": "2076",
          "emptyingDuration": "2520",
          "vatpH": "6.39"
      },
      {
          "vatNo": "6",
          "sequenceNo": "34",
          "recipeNo": "2",
          "DateTime": "2025-04-24 04:28:51",
          "fillingStopTime": "1856",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "33.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "767",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1907",
          "coagulationStartTime": "2222",
          "coagulationStopTime": "4314",
          "cuttingStartTime": "4376",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4556",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4950",
          "cookingStopTime": "7200",
          "cookingRPM": "5",
          "cookingTemperature": "38.6",
          "emptyingStartTime": "8714",
          "emptyingStopTime": "10774",
          "residenceTimeInVat": "6858",
          "totalProcesstimeInVat": "10774",
          "coagulationDuration": "2092",
          "cookingDuration": "2250",
          "emptyingDuration": "2060",
          "vatpH": "6.39"
      },
      {
          "vatNo": "7",
          "sequenceNo": "35",
          "recipeNo": "2",
          "DateTime": "2025-04-24 05:00:02",
          "fillingStopTime": "1786",
          "fillingRPM": "5",
          "milkQuantityInVat": "9969",
          "temperatureOfMilkInVat": "32.9",
          "cultureQuantity": "200",
          "cultureAdditionTime": "895",
          "rennetQuantity": "200",
          "rennetAdditionTime": "1840",
          "coagulationStartTime": "2154",
          "coagulationStopTime": "4245",
          "cuttingStartTime": "4308",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "4488",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "4882",
          "cookingStopTime": "6954",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "8557",
          "emptyingStopTime": "10338",
          "residenceTimeInVat": "6771",
          "totalProcesstimeInVat": "10338",
          "coagulationDuration": "2091",
          "cookingDuration": "2072",
          "emptyingDuration": "1781",
          "vatpH": "6.39"
      },
      {
          "vatNo": "2",
          "sequenceNo": "2",
          "recipeNo": "1",
          "DateTime": "2025-04-24 12:56:25",
          "fillingStopTime": "2012",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.5",
          "cultureQuantity": "200",
          "cultureAdditionTime": "302",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2654",
          "coagulationStartTime": "3004",
          "coagulationStopTime": "5083",
          "cuttingStartTime": "5158",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5331",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5719",
          "cookingStopTime": "7975",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9824",
          "emptyingStopTime": "12430",
          "residenceTimeInVat": "7812",
          "totalProcesstimeInVat": "12430",
          "coagulationDuration": "2079",
          "cookingDuration": "2256",
          "emptyingDuration": "2606",
          "vatpH": "6.41"
      },
      {
          "vatNo": "3",
          "sequenceNo": "3",
          "recipeNo": "1",
          "DateTime": "2025-04-24 13:29:58",
          "fillingStopTime": "2014",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "299",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2663",
          "coagulationStartTime": "2977",
          "coagulationStopTime": "5055",
          "cuttingStartTime": "5118",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5298",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5692",
          "cookingStopTime": "7888",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "10203",
          "emptyingStopTime": "12110",
          "residenceTimeInVat": "8189",
          "totalProcesstimeInVat": "12110",
          "coagulationDuration": "2078",
          "cookingDuration": "2196",
          "emptyingDuration": "1907",
          "vatpH": "6.43"
      },
      {
          "vatNo": "4",
          "sequenceNo": "4",
          "recipeNo": "1",
          "DateTime": "2025-04-24 14:03:48",
          "fillingStopTime": "1989",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "31.2",
          "cultureQuantity": "200",
          "cultureAdditionTime": "291",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2586",
          "coagulationStartTime": "2931",
          "coagulationStopTime": "5109",
          "cuttingStartTime": "5240",
          "cuttingRPM": "7",
          "intermittentCuttingStartTime": "5240",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5634",
          "cookingStopTime": "7698",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9871",
          "emptyingStopTime": "11844",
          "residenceTimeInVat": "7882",
          "totalProcesstimeInVat": "11844",
          "coagulationDuration": "2178",
          "cookingDuration": "2064",
          "emptyingDuration": "1973",
          "vatpH": "6.44"
      },
      {
          "vatNo": "5",
          "sequenceNo": "5",
          "recipeNo": "1",
          "DateTime": "2025-04-24 14:37:13",
          "fillingStopTime": "1989",
          "fillingRPM": "5",
          "milkQuantityInVat": "11217",
          "temperatureOfMilkInVat": "31.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "289",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2586",
          "coagulationStartTime": "3104",
          "coagulationStopTime": "5004",
          "cuttingStartTime": "5066",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5246",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5640",
          "cookingStopTime": "7715",
          "cookingRPM": "5",
          "cookingTemperature": "38.9",
          "emptyingStartTime": "9635",
          "emptyingStopTime": "11935",
          "residenceTimeInVat": "7646",
          "totalProcesstimeInVat": "11935",
          "coagulationDuration": "1900",
          "cookingDuration": "2075",
          "emptyingDuration": "2300",
          "vatpH": "6.47"
      },
      {
          "vatNo": "6",
          "sequenceNo": "6",
          "recipeNo": "1",
          "DateTime": "2025-04-24 15:10:38",
          "fillingStopTime": "2143",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "32.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "290",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2741",
          "coagulationStartTime": "3073",
          "coagulationStopTime": "5135",
          "cuttingStartTime": "5219",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5396",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5790",
          "cookingStopTime": "7852",
          "cookingRPM": "5",
          "cookingTemperature": "39.1",
          "emptyingStartTime": "9718",
          "emptyingStopTime": "12217",
          "residenceTimeInVat": "7575",
          "totalProcesstimeInVat": "12217",
          "coagulationDuration": "2062",
          "cookingDuration": "2062",
          "emptyingDuration": "2499",
          "vatpH": "6.44"
      },
      {
          "vatNo": "7",
          "sequenceNo": "7",
          "recipeNo": "1",
          "DateTime": "2025-04-24 15:46:36",
          "fillingStopTime": "1974",
          "fillingRPM": "5",
          "milkQuantityInVat": "11078",
          "temperatureOfMilkInVat": "32.6",
          "cultureQuantity": "200",
          "cultureAdditionTime": "298",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2630",
          "coagulationStartTime": "2976",
          "coagulationStopTime": "5067",
          "cuttingStartTime": "5188",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5364",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5758",
          "cookingStopTime": "7819",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9655",
          "emptyingStopTime": "11775",
          "residenceTimeInVat": "7681",
          "totalProcesstimeInVat": "11775",
          "coagulationDuration": "2091",
          "cookingDuration": "2061",
          "emptyingDuration": "2120",
          "vatpH": "6.43"
      },
      {
          "vatNo": "1",
          "sequenceNo": "8",
          "recipeNo": "1",
          "DateTime": "2025-04-24 16:19:31",
          "fillingStopTime": "1989",
          "fillingRPM": "5",
          "milkQuantityInVat": "11486",
          "temperatureOfMilkInVat": "34",
          "cultureQuantity": "200",
          "cultureAdditionTime": "299",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2589",
          "coagulationStartTime": "2934",
          "coagulationStopTime": "5012",
          "cuttingStartTime": "5075",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5255",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5649",
          "cookingStopTime": "7714",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9583",
          "emptyingStopTime": "12520",
          "residenceTimeInVat": "7594",
          "totalProcesstimeInVat": "12520",
          "coagulationDuration": "2078",
          "cookingDuration": "2065",
          "emptyingDuration": "2937",
          "vatpH": "6.44"
      },
      {
          "vatNo": "2",
          "sequenceNo": "9",
          "recipeNo": "1",
          "DateTime": "2025-04-24 16:58:28",
          "fillingStopTime": "1996",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "34.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "299",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2596",
          "coagulationStartTime": "2910",
          "coagulationStopTime": "4977",
          "cuttingStartTime": "5040",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5219",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5631",
          "cookingStopTime": "8294",
          "cookingRPM": "5",
          "cookingTemperature": "38.8",
          "emptyingStartTime": "9935",
          "emptyingStopTime": "11940",
          "residenceTimeInVat": "7939",
          "totalProcesstimeInVat": "11940",
          "coagulationDuration": "2067",
          "cookingDuration": "2663",
          "emptyingDuration": "2005",
          "vatpH": "6.43"
      },
      {
          "vatNo": "3",
          "sequenceNo": "10",
          "recipeNo": "1",
          "DateTime": "2025-04-24 17:33:57",
          "fillingStopTime": "1994",
          "fillingRPM": "5",
          "milkQuantityInVat": "11125",
          "temperatureOfMilkInVat": "35.4",
          "cultureQuantity": "200",
          "cultureAdditionTime": "272",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2581",
          "coagulationStartTime": "2974",
          "coagulationStopTime": "5040",
          "cuttingStartTime": "5117",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5283",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5677",
          "cookingStopTime": "7762",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9598",
          "emptyingStopTime": "12162",
          "residenceTimeInVat": "7604",
          "totalProcesstimeInVat": "12162",
          "coagulationDuration": "2066",
          "cookingDuration": "2085",
          "emptyingDuration": "2564",
          "vatpH": "6.43"
      },
      {
          "vatNo": "4",
          "sequenceNo": "11",
          "recipeNo": "1",
          "DateTime": "2025-04-24 18:07:11",
          "fillingStopTime": "1985",
          "fillingRPM": "5",
          "milkQuantityInVat": "11031",
          "temperatureOfMilkInVat": "33.3",
          "cultureQuantity": "200",
          "cultureAdditionTime": "286",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2583",
          "coagulationStartTime": "3000",
          "coagulationStopTime": "5078",
          "cuttingStartTime": "5367",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5545",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5950",
          "cookingStopTime": "8128",
          "cookingRPM": "5",
          "cookingTemperature": "39",
          "emptyingStartTime": "9956",
          "emptyingStopTime": "11889",
          "residenceTimeInVat": "7971",
          "totalProcesstimeInVat": "11889",
          "coagulationDuration": "2078",
          "cookingDuration": "2178",
          "emptyingDuration": "1933",
          "vatpH": "6.46"
      },
      {
          "vatNo": "5",
          "sequenceNo": "12",
          "recipeNo": "1",
          "DateTime": "2025-04-24 18:40:33",
          "fillingStopTime": "1988",
          "fillingRPM": "5",
          "milkQuantityInVat": "11171",
          "temperatureOfMilkInVat": "32.7",
          "cultureQuantity": "200",
          "cultureAdditionTime": "288",
          "rennetQuantity": "200",
          "rennetAdditionTime": "2588",
          "coagulationStartTime": "2905",
          "coagulationStopTime": "4996",
          "cuttingStartTime": "5093",
          "cuttingRPM": "2",
          "intermittentCuttingStartTime": "5269",
          "intermittentCuttingRPM": "7",
          "cookingStartTime": "5664",
          "cookingStopTime": "7723",
          "cookingRPM": "5",
          "cookingTemperature": "39.3",
          "emptyingStartTime": "9666",
          "emptyingStopTime": "12014",
          "residenceTimeInVat": "7678",
          "totalProcesstimeInVat": "12014",
          "coagulationDuration": "2091",
          "cookingDuration": "2059",
          "emptyingDuration": "2348",
          "vatpH": "6.43"
      }
  ]
  return generateResponse(res, 200, 'mmmmmmmmmm', data);
      const { csvId } = req.params;
      const {id} = req.user
      const fileRecord = await CSVFilesSchema.findOne({ 
          where: { 
              csv_id: csvId,
          } 
      });

      if (!fileRecord) {
          return generateResponse(res, 404, 'File not found.')
      }
      
      const key = fileRecord?.file_path;
      const bucketName = await gets3BucketName(fileRecord.tenant_id);
      const params = { Bucket: bucketName, Key: key };
      const s3Client = await getS3ClientForTenant(fileRecord.tenant_id);

      // Get file from S3
      const { Body } = await s3Client.send(new GetObjectCommand(params));
  
      const results = [];
      let hasDateTimeColumn = false;

      await new Promise((resolve, reject) => {
          Body.pipe(csv())
              .on("data", (data) => {
                  results.push(data);
                  // Check if DateTime column exists in the first row
                  if (results.length === 1) {
                      hasDateTimeColumn = 'DateTime' in data;
                  }
              })
              .on("end", () => {
                  // Only sort if DateTime column exists
                  if (hasDateTimeColumn) {
                      results.sort((a, b) => {
                          const dateA = new Date(a.DateTime);
                          const dateB = new Date(b.DateTime);
                          return dateA - dateB; // ascending order
                      });
                      resolve();
                  } else {
                      // If no DateTime column, keep original order
                      resolve();
                  }
              })
              .on("error", (error) => {
                  console.error("❌ CSV Parsing Error:", error);
                  reject(error);
              });
      });

      // Send response with appropriate message
      const message = hasDateTimeColumn 
          ? "File fetched and sorted by DateTime successfully"
          : "File fetched successfully";
          
      return generateResponse(res, 200, message, results);
      
  } catch (error) {
      console.error('error :', error);
      return generateResponse(res, 500, 'Failed to retrieve file.')
  }
}


const getFileList = async (req, res) => {
  const {id , tenant_id} = req.user
    try {
      const systemsIdParam = req.query?.systems_id;

      // Extract the shared workflows and workflowStructre
      const workflowStructures = await sequelize.query(
        `
        select  w2."hierarchy" from workflow_shared ws 
        inner join workflows w ON CAST(w.id AS VARCHAR) = ws.share_type_id
        inner join workflowstructures w2 on w2.workflow_id = w.id
        where (
          (ws.share_type = 'tenant' AND ws.shared_to_id = ${tenant_id}) OR
          (ws.share_type = 'users' AND (ws.shared_to_id = ${id} OR ws.user_id = ${id}))
        )
        `,
        {
          type: sequelize.QueryTypes.SELECT,
        }
      );

      let dataIds = []
      if (workflowStructures) {  // Extract the csv_id of those files which are included in the the sharable workflow
         dataIds = workflowStructures
          ?.flatMap(item => 
            item?.hierarchy?.nodes?.filter(node => node?.data?.type === "file")
          )
          .map(node => node?.data?.id);
      }

      const whereConditions = {
        [Op.or]: [
          {
            [Op.and]: [
              { user_id: id },
              { is_deleted: false },
              { hide_for_s3: false }
            ]
          },
        ]
      };
      if (dataIds.length > 0) {
        whereConditions[Op.or].push({ csv_id: { [Op.in]: dataIds } });
      }

      if (systemsIdParam && typeof systemsIdParam === 'string') {
        const systemsNameArray = systemsIdParam.split(',').map((name) => name.trim());
        
        if (systemsNameArray.length > 0) {
          const sortedSystemsNameArray = systemsNameArray.sort();
          const arrayLiteral = `{${sortedSystemsNameArray.join(',')}}`;
      
          whereConditions.systems_name =  sequelize.literal(
              `"CSVFiles"."systems_id" @> '${arrayLiteral}' AND "CSVFiles"."systems_id" <@ '${arrayLiteral}'`
            );
        }
      }



      const files = await CSVFilesSchema.findAll({
        attributes: [
          'csv_id', 
          'file_name', 
          'file_path', 
          'version',
          'compatibility', 
          'created_at', 
          'updated_at', 
          'parent_file_id', 
          'type_of_file',
          'clustering_data_response'
        ],
        where: whereConditions,
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['first_name', 'last_name']
              }
          ],
        order: [['created_at', 'DESC']]
      });
  
      if (!files.length) {
        return generateResponse(res, 200, 'No files are available', { files: [] });
      }
  
      const filesWithColumns = await Promise.all(files.map(async (file) => {
        return {
          csv_id: file.csv_id,
          file_name: file.file_name,
          file_path: file.file_path,
          version: file.version,
          owner:file.user,
          compatibility:file.compatibility,
          created_at: file.created_at,
          updated_at: file.updated_at,
          parent_file_id: file.parent_file_id || null,
          type_of_file: file.type_of_file,
          clustering_data_response: file.clustering_data_response,
          // columns: columns // Add columns to response
        };
      }));
  
      const response = {
        files: filesWithColumns
      };
      return generateResponse(res, 200, 'File list is fetched successfully', response);
  
    } catch (error) {
      console.error('Error fetching files:', error);
      return generateResponse(res, 500, 'Failed to fetch files');
    }
  };


  const getFileColumns = async (req, res) => {
    try {
      const { csvId } = req.params;
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(csvId)) {
        return generateResponse(res, 404, `File with csvid ${csvId} not found`);
      }
      const file = await CSVFilesSchema.findOne({
        attributes: ['csv_id', 'file_name', 'file_path', 'version', 'tenant_id', 'type_of_file'],
        where: { csv_id: csvId }
      });
      if (!file) {
        return generateResponse(res, 404, `File with csvid ${csvId} not found`);
      }
  
      const key = file?.file_path;

      const bucketName = await gets3BucketName(file?.tenant_id);
      const params = { Bucket: bucketName, Key: key };
      const s3Client = await getS3ClientForTenant(file?.tenant_id);
      const { Body } = await s3Client.send(new GetObjectCommand(params));
  
      let columns = [];
      let firstThreeRows = [];
      let datetimeFormats = {};
  
      const processFile = new Promise((resolve, reject) => {
        const stream = Body.pipe(csv());
  
        stream.on('headers', (headers) => {
          columns = headers;
        });
  
        stream.on('data', (row) => {
          if (firstThreeRows.length < 3) {
            firstThreeRows.push(row);
            
            if (row?.DateTime) {
              const dateOnly = row.DateTime.split(' ')[0]; // Extract YYYY-MM-DD only
              const detectedFormat = detectDateFormat(dateOnly);
              datetimeFormats[detectedFormat] = (datetimeFormats[detectedFormat] || 0) + 1;
            }
          }
        });
  
        stream.on('end', () => {
          const mostCommonFormat = Object.entries(datetimeFormats).sort((a, b) => b[1] - a[1])[0]?.[0] || null;
          resolve({ columns, firstThreeRows, mostCommonFormat });
        });
  
        stream.on('error', (error) => {
          console.error('Error reading file:', error);
          reject(error);
        });
      });
  
      let { columns: columnsData, firstThreeRows: rowsData, mostCommonFormat } = await processFile;
      let response = {
        file,
        columns: columnsData,
        mostCommonDateTimeFormat: mostCommonFormat
      };
      return generateResponse(res, 200, 'Successfully fetched file columns and datetime format', response);
  
    } catch (error) {
      console.log('error :', error);
      return generateResponse(res, 500, 'Something went wrong');
    }
  };

  const detectDateFormat = (dateString) => {
    const regexFormats = [
      { regex: /^\d{4}-\d{2}-\d{2}$/, format: 'YYYY-MM-DD' },
      { regex: /^\d{2}\/\d{2}\/\d{4}$/, format: 'MM/DD/YYYY' },
      { regex: /^\d{2}-\d{2}-\d{4}$/, format: 'DD-MM-YYYY' }
    ];
  
    for (let { regex, format } of regexFormats) {
      if (regex.test(dateString)) {
        return format;
      }
    }
    return 'Unknown Format';
  };

  const removeFile = async (req, res) => {
    const { csv_id } = req.params;

    try {
        const fileRecord = await CSVFilesSchema.findOne({ where: { csv_id } });

        if (!fileRecord) {
            return res.status(404).json({ message: 'File not found' });
        }

        const referencedWorkflows = await WorkflowComponents.findAll({
            where: { component: csv_id },
            attributes: ['workflow_id']
        });

        if (referencedWorkflows.length > 0) {
            await CSVFilesSchema.update(
                { is_deleted: true },
                { where: { csv_id } }
            );

            const workflowIds = referencedWorkflows.map((record) => record.workflow_id);
            return res.status(200).json({
                message: 'File is referenced in workflows and has been soft deleted',
                status: 200,
                workflowIds,
            });
        }

        const bucketName = await gets3BucketName(fileRecord.tenant_id);
        console.log('bucketName', bucketName)

        // Delete the file from S3
        const deleteParams = {
            Bucket: bucketName,
            Key: fileRecord.file_path,
        };

        const s3Client = await getS3ClientForTenant(fileRecord?.tenant_id);

        await s3Client.send(new DeleteObjectCommand(deleteParams));

        await CSVFilesSchema.destroy({ where: { csv_id } });

        return res.status(200).json({
            message: 'File successfully deleted',
            status: 200
        });
    } catch (error) {
        console.error('Error deleting file:', error);
        return res.status(500).json({ message: 'Internal server error', error });
    }
};


const mapCsvFields = async (req, res) => {
  try {
    const { csvId } = req.params;
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    
    if (!uuidRegex.test(csvId)) {
      return generateResponse(res, 404, `File with csvid ${csvId} not found`);
    }
    
    if (!req.body?.headerMapping) {
      return generateResponse(res, 422, 'Missing fields headerMapping');
    }
    
    const headerMapping = req.body.headerMapping;

    const fileRecord = await CSVFilesSchema.findOne({
      where: { csv_id: csvId }
    });

    if (!fileRecord) {
      return generateResponse(res, 404, `File with csvid ${csvId} not found`);
    }
    const bucketName = await gets3BucketName(fileRecord.tenant_id);

    const key = fileRecord?.file_path;
    const params = { Bucket: bucketName, Key: key };

    const s3Client = await getS3ClientForTenant(fileRecord?.tenant_id);

    // ✅ Get file from S3
    const { Body } = await s3Client.send(new GetObjectCommand(params));

    // Read and parse the CSV file
    const rows = [];
    let originalHeaders = [];

    await new Promise((resolve, reject) => {
      Body.pipe(csv())
        .on('headers', (headerList) => {
          originalHeaders = headerList; // Store original headers
        })
        .on('data', (row) => {
          rows.push(row); // Collect row data as-is
        })
        .on('end', resolve)
        .on('error', reject);
    });

    // ✅ Ensure headers are renamed correctly
    const newHeaders = originalHeaders.map((header) => headerMapping[header] || header);

    // ✅ Prepare CSV writer with updated headers
    const csvWriter = createObjectCsvWriter({
      path: `/tmp/${fileRecord.csv_id}.csv`,
      header: newHeaders.map((newHeader, index) => ({
        id: originalHeaders[index], // Keep original field mapping
        title: newHeader, // Rename only the title
      })),
    });

    // ✅ Write records back to the CSV file
    await csvWriter.writeRecords(rows);

    // ✅ Upload the modified file back to S3
    const modifiedFilePath = `/tmp/${fileRecord.csv_id}.csv`;
    const modifiedFileContent = fs.readFileSync(modifiedFilePath);
    const uploadParams = {
      Bucket: bucketName,
      Key: key,
      Body: modifiedFileContent,
    };

    await s3Client.send(new PutObjectCommand(uploadParams));

    // ✅ Clean up the temporary file
    if (fs.existsSync(modifiedFilePath)) {
      fs.unlinkSync(modifiedFilePath);
    }

    // ✅ Reverse mapping for database storage
    const reversedMapping = Object.fromEntries(
      Object.entries(headerMapping).map(([key, value]) => [value, key])
    );

    try {
      // Update compatibility in CSVFilesSchema
      await CSVFilesSchema.update(
        { compatibility: true },
        { where: { csv_id: csvId } }
      );

      // Upsert columns mapping in CsvMapping table
      const existingRow = await CsvMapping.findOne({
        where: { tenant_id: req.user.tenant_id },
      });

      if (existingRow) {
        const updatedColumnsMapping = {
          ...existingRow.columns_mapping,
          ...reversedMapping,
        };
        await CsvMapping.update(
          { columns_mapping: updatedColumnsMapping },
          { where: { tenant_id: req.user.tenant_id } }
        );
      } else {
        await CsvMapping.create({
          tenant_id: req.user.tenant_id,
          columns_mapping: reversedMapping,
        });
      }

      // After setting compatibility to true hit the AI/Ml API
      if (fileRecord.type_of_file.includes('material')) {
        console.log('@@@Material file detected, processing...');
        await processMaterialFile(fileRecord.aws_file_link, fileRecord.csv_id, fileRecord.type_of_file);
      }

      return generateResponse(res, 200, 'CSV Mapping updated successfully');
    } catch (error) {
      console.error('Error upserting CSV mapping:', error);
      return generateResponse(res, 500, 'Error upserting CSV mapping:');
    }
  } catch (error) {
    console.error(error);
    return generateResponse(res, 500, 'Internal server error while CSV Mapping');
  }
};



const csvMappingHistory = async (req , res) =>{
  try {
    const {tenant_id} = req.user
    const columns = req.body?.columns ? req.body.columns : [];
    if(!columns.length){
      return generateResponse(res, 400, 'Missing required parameter columns')
    }
    const CsvMappingResults = await CsvMapping.findAll({
      where:{tenant_id : tenant_id},
      attributes: columns.map(column => [
        sequelize.json(`columns_mapping.${column}`),
        column,
      ]),
    });
    return generateResponse(res, 200, 'CSV Mapping History fetched successfully',CsvMappingResults)
  } catch (error) {
  console.log('CSV Mapping History fetched error :', error);
  return generateResponse(res, 500, 'CSV Mapping History fetched server error')
    
  }
}

const updateFile = async (req, res) => {
  const { csv_id } = req.body;
  const { tenant_id, id } = req.user;

  try {
    // Find the existing file record by csv_id
    const fileRecord = await CSVFilesSchema.findOne({
      where: { csv_id, tenant_id },
    });

    if (!fileRecord) {
      return generateResponse(res, 404, "File not found");
    }

    // Delete the existing file from the file system (if needed)
    fs.unlinkSync(fileRecord.file_path); // Use your preferred method to delete the old file

    // Update the file record with new data
    const updatedFile = await CSVFilesSchema.update(
      {
        file_name: req.file.originalname,
        file_path: req.file.path,
        path_for_aiml: process.env.FILE_PATH_FOR_AIML + req.file.filename,
        version: fileRecord.version + 1, // Increment version
        description: req.body.description || fileRecord.description,
      },
      { where: { csv_id } }
    );

    if (updatedFile) {
      return generateResponse(res, 200, "File updated successfully", {
        csvId: csv_id,
      });
    } else {
      return generateResponse(res, 500, "Failed to update file");
    }
  } catch (error) {
    console.error("Error updating file:", error);
    return generateResponse(res, 500, "Failed to update file");
  }
};




const createClusterAllRunCSVFile = async (req, res) => {
  try {
    let UserSystemSelected = await User.findOne({ where: { id: req.user.id } });
    const selected_systems = UserSystemSelected.selected_systems;
    const getAllSystems = await Systems.findAll({});
    const selectedNames = selected_systems
      .map(id => getAllSystems.find(system => system.id == id)?.name)
      .filter(Boolean)
      .join(" + ");

    console.log("selectedNames", selectedNames);
    const { id, tenant_id } = req.user;
    const { clusterAllRunData, fileName, fileId } = req.body;

    if (!clusterAllRunData) {
      return generateResponse(res, 404, 'Cluster all run data is not available.');
    }

    const filePath = Date.now();
    const outputFilePath = path.join(process.cwd(), 'public', 'datasource', `${filePath}.csv`);

    const headers = Object.keys(clusterAllRunData);
    const rowIndices = Array.from(new Set(
      headers.flatMap(header => Object.keys(clusterAllRunData[header]).map(Number))
    )).sort((a, b) => a - b);
    const rows = rowIndices.map(index =>
      headers.map(header => clusterAllRunData[header][index] ?? '').join(',')
    );
    const csvContent = [headers.join(','), ...rows].join('\n');

    fs.writeFile(outputFilePath, csvContent, 'utf8', async (err) => {
      if (err) {
        console.error('Error writing Cluster all run CSV file:', err);
        return generateResponse(res, 500, 'Error writing Cluster all run CSV file:');
      } else {
        try {
          const systems = Array.isArray(req.body.systems) ? req.body.systems : JSON.parse(req.body.systems || '[]');
          let systemIds = [];
          if (systems && systems.length) {
            systemIds = systems.map(item => item.systemId);
          }
          const compatibility = true;

          // Upload the file to S3
          let fileKey = uuidv4();
          const fileKeyToSave = fileKey
  
          let tenantData = await Tenants.findOne({ where: {id: tenant_id}})
          let awsCredentials = tenantData.dataValues.aws_credentials
  
          // fileKey = `data/data=file/${folderName}/${fileKey}`
          fileKey = `data/data=file/${selectedNames}/${fileKey}`
          
          const fileUrl = await uploadFileToS3(outputFilePath, fileKey, awsCredentials);

          if (fileUrl) {
            // Remove the file from local storage
            fs.unlink(outputFilePath, (err) => {
              if (err) {
                console.error('Error removing file from local storage:', err);
              } else {
                console.log('File removed from local storage:', outputFilePath);
              }
            });

            const uploadedFile = await CSVFilesSchema.create({
              user_id: id,
              file_name: fileName,
              path_for_aiml: process.env.FILE_PATH_FOR_AIML + `${filePath}.csv`,
              file_path: fileKey,
              description: 'File is created and uploaded using cluster run data' || null,
              version: req.body.version || 1,
              tenant_id: tenant_id,
              systems_id: systemIds,
              compatibility: compatibility,
              csv_id: fileKeyToSave,
              aws_file_link: fileUrl,
              parent_file_id: fileId,
              type_of_file: req.body.type_of_file
            });

            const fileResponse = {
              csvId: uploadedFile.get('csv_id'),
            };
            return generateResponse(res, 200, 'Cluster all run File created & uploaded successfully.', fileResponse);
          } else {
            return generateResponse(res, 500, 'Failed to upload Cluster all run file.');
          }
        } catch (error) {
          console.log('error :', error);
          return generateResponse(res, 500, 'Failed to upload Cluster all run file.');
        }
      }
    });
  } catch (error) {
    console.log('error :', error);
    return generateResponse(res, 500, "Error while uploading Cluster all run CSV file:");
  }
};

const getS3ClientForTenant = async (tenantId) => {
  try {
    // Fetch the tenant record
    const tenant = await Tenants.findOne({
      where: { id: tenantId },
      attributes: ["aws_credentials"], // Fetch only the credentials field
    });

    if (!tenant || !tenant.aws_credentials) {
      throw new Error("AWS credentials not found for tenant.");
    }
    const AWS_REGION = tenant?.aws_credentials?.AWS_REGION;

    if ( !AWS_REGION) {
      throw new Error("Invalid AWS credentials format.");
    }

    // Initialize and return the S3 client
    return new S3Client({
      region: AWS_REGION,
      // credentials: {
      //   accessKeyId: AWS_ACCESS_KEY_ID,
      //   secretAccessKey: AWS_SECRET_ACCESS_KEY,
      // },
    });
  } catch (error) {
    console.log('errorrrr', error)
    console.error("Error initializing S3 client:", error.message);
    throw error;
  }
};

const gets3BucketName = async (tenantId) => {
  try {
    // Fetch the tenant record
    const tenant = await Tenants.findOne({
      where: { id: tenantId },
      attributes: ["aws_credentials"], // Fetch only the credentials field
    });

    console.log('tenant', tenant)
    if (!tenant || !tenant.aws_credentials) {
      throw new Error("AWS credentials not found for tenant.");
    }

    // Parse AWS credentials (assuming stored as JSON)
    let AWS_BUCKET_NAME = tenant?.aws_credentials?.AWS_BUCKET_NAME

    if (!AWS_BUCKET_NAME) {
      throw new Error("Invalid AWS credentials format.");
    }

    // Initialize and return the S3 client
    return AWS_BUCKET_NAME
  } catch (error) {
    console.error("Error initializing S3 bucket client:", error.message);
    throw error;
  }
};

const getReportData = async (req, res) => {
  try {
    const { start_date, end_date, tableName } = req.body; // Get start and end dates from the request body

    // Validate the input dates
    if (!start_date || !end_date) {
      return generateResponse(res, 400, "Start date and end date are required.");
    }

    // Get the column data types for the table
    const columnInfo = await sequelize.query(
      `
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = :tableName
      `,
      {
        replacements: { tableName },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    // Query the report_data table for records within the date range
    const reportData = await sequelize.query(
      `
      SELECT *
      FROM ${tableName}
      WHERE "DateTime" BETWEEN :start_date AND :end_date
      `,
      {
        replacements: { start_date, end_date },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    // Check if data exists
    if (!reportData.length) {
      return generateResponse(res, 200, "No data found for the selected date range.", []);
    }

    // Format the column info to include data types
    const columnsWithTypes = columnInfo.map((column) => ({
      columnName: column.column_name,
      dataType: column.data_type,
    }));

    // Return the fetched data along with column data types
    return generateResponse(res, 200, "Report data fetched successfully.", {
      data: reportData,
      columns: columnsWithTypes,
    });
  } catch (error) {
    console.error("Error fetching report data:", error);
    return generateResponse(res, 500, "Failed to fetch report data.");
  }
};


const uploadJsonFile = async (req, res) => {
  try {

    let fullJson = req.body.annotations;
    let columnName = req.body.columnName;

    const systems = Array.isArray(req.body.systems) ? req.body.systems: JSON.parse(req.body.systems || '[]');
    let systemNames = []
    if(systems && systems.length){
      systemNames = systems.map(item => item.systemName);
    }
    const folderName = systemNames.join(" + ");
    console.log('folderName', folderName)

    const {tenant_id} = req.user
    let tenantData = await Tenants.findOne({ where: {id: tenant_id}})
    let awsCredentials = tenantData.dataValues.aws_credentials

    // if (!fullJson?.annotations || !Array.isArray(fullJson.annotations)) {
    //   return res.status(400).json({ success: false, message: 'Invalid annotations array.' });
    // }

    const annotationsArray = req.body.annotations;

    const anomalyAnnotations = annotationsArray?.filter(item => item.operationType === 'anomaly');
    const operationAnnotations = annotationsArray?.filter(item => item.operationType === 'operation');
    
    const s3Storage = new S3Client({
      region: awsCredentials?.AWS_REGION,
    });
    const bucketName = awsCredentials?.AWS_BUCKET_NAME;

    const anomalyKey = `data/data=file/${folderName}/${columnName}/anomaly.json`;
    const operationKey = `data/data=file/${folderName}/${columnName}/operation.json`;

    const deleteIfExists = async (key) => {
      try {
        await s3Storage.send(new HeadObjectCommand({
          Bucket: bucketName,
          Key: key
        }));
        // If HeadObjectCommand succeeds, file exists - delete it
        await s3Storage.send(new DeleteObjectCommand({
          Bucket: bucketName,
          Key: key
        }));
        console.log(`Deleted existing file: ${key}`);
      } catch (err) {
        if (err.name !== 'NotFound') {
          throw err; // If error is not "NotFound", rethrow
        }
        console.log(`File does not exist: ${key}`);
      }
    };

    // Check and delete existing files
    await deleteIfExists(anomalyKey);
    await deleteIfExists(operationKey);

    // Upload anomaly file
    let upload = await s3Storage.send(new PutObjectCommand({
      Bucket: bucketName,
      Key: anomalyKey,
      Body: JSON.stringify(anomalyAnnotations, null, 2),
    }));
    console.log('upload', upload)

    // Upload operation file
    let upload1 = await s3Storage.send(new PutObjectCommand({
      Bucket: bucketName,
      Key: operationKey,
      Body: JSON.stringify(operationAnnotations, null, 2),
    }));
    console.log('upload1', upload1)

    return res.status(200).json({
      message: 'JSON files uploaded successfully.',
      files: {
        anomalyFile: anomalyKey,
        operationFile: operationKey
      }
    });

  } catch (error) {
    console.error('Error uploading JSON files:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
};


const getJsonFileData = async (req, res) => {
  try {
    const { columnName, systems } = req.body;
    const { tenant_id } = req.user;

    if (!columnName || !systems) {
      return res.status(400).json({ success: false, message: 'Missing columnName or systems.' });
    }

    const systemsArray = Array.isArray(systems)
      ? systems
      : JSON.parse(systems || '[]');

    const folderName = systemsArray.map(item => item.systemName).join(' + ') || 'unknown_system';

    const tenantData = await Tenants.findOne({ where: { id: tenant_id } });
    if (!tenantData) {
      return res.status(404).json({ success: false, message: 'Tenant not found.' });
    }

    const awsCredentials = tenantData.dataValues.aws_credentials;

    const s3 = new S3Client({
      region: awsCredentials.AWS_REGION,
      
    });

    const bucketName = awsCredentials.AWS_BUCKET_NAME;

    const anomalyKey = `data/data=file/${folderName}/${columnName}/anomaly.json`;
    const operationKey = `data/data=file/${folderName}/${columnName}/operation.json`;

    const [anomalyResponse, operationResponse] = await Promise.all([
      s3.send(new GetObjectCommand({ Bucket: bucketName, Key: anomalyKey })),
      s3.send(new GetObjectCommand({ Bucket: bucketName, Key: operationKey }))
    ]);

    const anomalyBuffer = await streamToBuffer(anomalyResponse.Body);
    const operationBuffer = await streamToBuffer(operationResponse.Body);

    const anomalyData = JSON.parse(anomalyBuffer.toString());
    const operationData = JSON.parse(operationBuffer.toString());

    return res.status(200).json({
      success: true,
      data: {
        anomaly: anomalyData,
        operation: operationData
      }
    });

  } catch (error) {
    console.error('Error retrieving JSON files:', error);
    return res.status(500).json({ success: false, message: error.message });
  }
};

const streamToBuffer = async (stream) => {
  const chunks = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
};

const updateJsonFile = async (req, res) => {

  try {
    const { filename, updatedJsonData } = req.body;

    if (!filename || !updatedJsonData) {
      return res.status(400).json({ success: false, error: 'Missing filename or updatedJsonData in request body.' });
    }

    const tenant_id = req.user.tenant_id;
    const tenantData = await Tenants.findOne({ where: { id: tenant_id } });
    const awsCredentials = tenantData?.dataValues?.aws_credentials;

    if (!awsCredentials) {
      throw new Error('AWS credentials not found for the tenant.');
    }

    const bucketName = awsCredentials.AWS_BUCKET_NAME;
    // const bucketName = 'testing-proj12';

    // Initialize S3 client
    const s3Client = new S3Client({
      region: awsCredentials.AWS_REGION,
    });

    // Delete the existing file from S3
    const deleteParams = {
      Bucket: bucketName,
      Key: filename,
    };

    try {
      await s3Client.send(new DeleteObjectCommand(deleteParams));
      console.log('✅ Existing file deleted from S3:', filename);
    } catch (deleteError) {
      console.error('❌ Error deleting file from S3:', deleteError);
      return res.status(500).json({ success: false, error: 'Failed to delete the existing file from S3.' });
    }

    // Upload the updated file to S3
    const uploadParams = {
      Bucket: bucketName,
      Key: filename,
      Body: JSON.stringify(updatedJsonData, null, 2),
    };

    try {
      await s3Client.send(new PutObjectCommand(uploadParams));
      console.log('✅ Updated file uploaded to S3:', filename);
    } catch (uploadError) {
      console.error('❌ Error uploading updated file to S3:', uploadError);
      return res.status(500).json({ success: false, error: 'Failed to upload the updated file to S3.' });
    }

    return res.status(200).json({
      success: true,
      message: 'JSON file updated successfully on S3.',
    });
  } catch (error) {
    console.error('Error updating JSON file on S3:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
};

const deleteJsonFile = async (req, res) => {
  try {
    const { filename } = req.params;

    if (!filename) {
      return res.status(400).json({ success: false, error: 'Filename is required.' });
    }

    const tenant_id = req.user.tenant_id;
    const tenantData = await Tenants.findOne({ where: { id: tenant_id } });
    const awsCredentials = tenantData?.dataValues?.aws_credentials;

    if (!awsCredentials) {
      throw new Error('AWS credentials not found for the tenant.');
    }

    const bucketName = awsCredentials.AWS_BUCKET_NAME;

    // Initialize S3 client
    const s3Client = new S3Client({
      region: awsCredentials.AWS_REGION,
    });

    // Define S3 parameters
    const params = {
      Bucket: bucketName,
      Key: filename,
    };

    // Delete the file from S3
    try {
      await s3Client.send(new DeleteObjectCommand(params));
      console.log('✅ File deleted from S3:', filename);
      return res.status(200).json({
        success: true,
        message: 'File deleted successfully from S3.',
      });
    } catch (deleteError) {
      console.error('❌ Error deleting file from S3:', deleteError);
      return res.status(500).json({ success: false, error: 'Failed to delete the file from S3.' });
    }
  } catch (error) {
    console.error('Error in deleteJsonFile:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
};

export {
  uploadFile,
  getFile,
  getFileList,
  getFileColumns,
  removeFile,
  mapCsvFields,
  csvMappingHistory,
  updateFile,
  createClusterAllRunCSVFile,
  getReportData,
  uploadJsonFile,
  getJsonFileData,
  updateJsonFile,
  deleteJsonFile
};