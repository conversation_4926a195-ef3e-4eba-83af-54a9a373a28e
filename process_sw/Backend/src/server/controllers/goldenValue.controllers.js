import Workflow from '../models/workflow.model.js';  // Adjust the path as needed
import WorkflowStructure from '../models/workflowStructure.model.js';
import { sequelize } from "../database/dbConnection.js";
import WorkflowComponents from '../models/workflowComponents.model.js';
import Operations from '../models/operation.model.js';
import axios from 'axios';
import M<PERSON><PERSON>ob from '../models/MLjobs.model.js';
import { where } from 'sequelize';
import CSVFilesSchema from '../models/file.model.js';
import GoldenValueData from '../models/goldenValueData.model.js';
import { transformFileSettings } from './workflow.controllers.js';

import workflowFilters from '../models/workflowFilters.model.js';
import { generateResponse } from '../utils/commonResponse.js';

// export const createGoldenValue = async (req, res) => {
//     console.log('req', req.user.id)
//     const { workflow_id } = req.body;
//     console.log('workflow_id', workflow_id)

//     try {
//         if (!workflow_id) {
//             return res.status(400).json({ error: 'workflow_id is required' });
//         }

//         const MljobData = await MLJob.findOne({workflow_id: workflow_id})
//         console.log('MljobData', MljobData)
//         let settings = MljobData.settings
        
//         const reponse = await GoldenValueData.create({
//             workflow_id,
//             settings,
//             golden_value: req.body.golden_values,
//             name: req.body.golden_name,
//             user_id: req.user.id
//         });

//         return res.status(200).json({
//             message: 'Data Saved successfully',
//             data: reponse,
//         });
//     } catch (error) {
//         console.error('Error creating workflow structure:', error);
//         return res.status(500).json({ error: 'Failed to create workflow structure' });
//     }
// };

export const createGoldenValue = async (req, res) => {
    const { workflow_id, golden_values, golden_name,golden_run_data ,cluster_data , mlJobRunId} = req.body;

    try {
        if (!workflow_id) {
            return res.status(400).json({ error: 'workflow_id is required' });
        }

        // Check if a record exists in GoldenValueData
        const existingGoldenValue = await GoldenValueData.findOne({ where: {workflow_id , mljob_run_id : mlJobRunId}  });

        if (existingGoldenValue) {

                existingGoldenValue.golden_value = golden_values;
                existingGoldenValue.name = golden_name;
                existingGoldenValue.golden_run_data = golden_run_data;
                existingGoldenValue.cluster_data = cluster_data;
                // existingGoldenValue.filter_id = existingGoldenValue.filter_id;
                await existingGoldenValue.save();
                return res.status(200).json({
                    message: 'Data updated successfully ',
                    data: existingGoldenValue,
                });
        } else {
            // If not found, fetch settings from MLJob and create a new entry
            const MljobData = await MLJob.findOne({ workflow_id }).sort({_id:-1}).limit(1);
            if (!MljobData) {
                return res.status(404).json({ error: 'MLJob not found with the given workflow_id' });
            }


            let workflowData = await Workflow.findOne({ where : {id: workflow_id }})
            let GlobalfilterData = await workflowFilters.findOne({ where: {id: workflowData?.filter_id}})

            const globalFilters = {};
            if (GlobalfilterData?.filters?.file?.global?.rules) {
                GlobalfilterData?.filters?.file?.global?.rules.forEach(rule => {
                    if (rule.field) {
                        globalFilters[rule.field] = {
                            operator: rule.operator === '=' ? '==' : rule.operator,
                            value: rule.operator === 'between' ? rule.value.split(',') :
                            rule.value
                        };
                    }
                });
            }

            const targetFilters = {};
            if (GlobalfilterData?.filters?.file?.target_variable_settings?.rules) {
                GlobalfilterData.filters.file.target_variable_settings.rules.forEach(rule => {
                    if (rule.field) {
                        targetFilters[rule.field] = {
                            operator: rule.operator === '=' ? '==' : rule.operator,
                            value: rule.operator === 'between' ? rule.value.split(',') :
                            rule.value
                        };
                    }
                });
            }

            if (GlobalfilterData?.filters?.file?.target_variable_settings?.rules) {
                MljobData.settings.datasource.target_variable_settings = targetFilters;
            }
            
            if (GlobalfilterData?.filters?.file?.global?.rules) {
                MljobData.settings.datasource.feature_filters = globalFilters;
            }

            const newEntry = await GoldenValueData.create({
                workflow_id,
                settings: MljobData.settings,
                golden_value: golden_values,
                name: golden_name,
                user_id: req.user.id,
                golden_run_data:golden_run_data,
                filter_id: MljobData.filter_id,
                cluster_data : cluster_data,
                mljob_run_id:mlJobRunId
            });

            return res.status(201).json({
                message: 'Data created successfully',
                data: newEntry,
            });
        }
    } catch (error) {
        console.error('Error creating or updating workflow structure:', error);
        return res.status(500).json({ error: 'Failed to create or update workflow structure' });
    }
};

export const createParameter = async (req, res) => {
    const { golden_values, golden_name, filterId, goldenId } = req.body;
    try {
        if (goldenId) {
            // Update the golden_value only
            const existingEntry = await GoldenValueData.findOne({ where: { id: goldenId } });
            if (!existingEntry) {
                return res.status(404).json({ error: 'Golden value not found' });
            }
            existingEntry.golden_value = golden_values;
            if(filterId){
             existingEntry.filter_id = filterId;
            }
            let transformed = transformFileSettings(req.body.settings, 'golden_batch');
            let datasource = { datasource: transformed };
            existingEntry.settings = datasource;
            await existingEntry.save();

            return res.status(200).json({
                message: 'Golden value updated successfully',
                data: existingEntry,
            });
        } else {
            // Create a new entry
            let transformed = transformFileSettings(req.body.settings, 'golden_batch');
            let datasource = { datasource: transformed };
            const newEntry = await GoldenValueData.create({
                golden_value: golden_values,
                name: golden_name,
                user_id: req.user.id,
                settings: datasource,
                custom_parameter: true,
                filter_id: filterId
            });

            return res.status(201).json({
                message: 'Data created successfully',
                data: newEntry,
            });
        }
    } catch (error) {
        console.error('Error creating or updating workflow structure:', error);
        return res.status(500).json({ error: 'Failed to create or update workflow structure' });
    }
};

export const getGoldenValues = async (req, res) => {
    const filters = req.query.filters;
        const id = req.user.id
        const { page = 1, pageSize = 100 } = filters || {};
    try {

        // let whereClause =`golden_value_data.user_id = ${id} `
        let whereClause = `golden_value_data.user_id = ${id} AND golden_value_data.hide_for_s3 = false `;

        if(filters?.filter_id){
            whereClause +=`AND filter_id = ${filters?.filter_id} `
        }
        console.log('filters?.file :', filters?.file);
        if(filters?.file){
            let filePath = {
                "datasource" : {
                }
            }
            const file = await CSVFilesSchema.findOne({
                where: { csv_id: filters?.file},
                attributes: ['csv_id', 'path_for_aiml', 'aws_file_link']
            });
            filePath.datasource.file_path = file?.dataValues?.aws_file_link
            whereClause += ` AND settings @> '${JSON.stringify(filePath)}'`
        }
        
        
        // // filter query  for selected Date 
        // if(filters?.dateRange){
        //     whereClause += ` AND (created_at::date BETWEEN '${filters?.dateRange[0]}' AND '${filters?.dateRange[1]}')`
        // }

        // // filter by file name matching aiml_path
        // if(filters?.file){
        //     let filePath = {
        //         "datasource" : {
        //         }
        //     }
        //     const file = await CSVFilesSchema.findOne({
        //         where: { csv_id: filters?.file},
        //         attributes: ['csv_id', 'path_for_aiml']
        //     });
        //     filePath.datasource.file_path = file?.dataValues?.path_for_aiml
        //     whereClause += ` AND settings @> '${JSON.stringify(filePath)}'`
        // }

        // // filter query for  target variable setting 
        // if(filters?.target_variable_settings){
        //     if(filters?.target_variable_settings?.rules && filters?.target_variable_settings?.rules[0]){
        //         let targetValue = {
        //             "datasource" : {
        //                 "target_variable_settings":{
        //                 }
        //             }
        //         }
        //         filters?.target_variable_settings.rules.forEach((target)=>{
        //             const operatorAndTargetValue = getOperator(target?.operator,target?.value)
        //             const result = {
        //                 [target.field]: operatorAndTargetValue ? operatorAndTargetValue : {}
        //             };
        //             // Merge the new result into the existing target_variable_settings (using spread to ensure no overwriting)
        //             targetValue.datasource.target_variable_settings[target.field] = result[target.field];
        //         })
        //         whereClause += ` AND settings @> '${JSON.stringify(targetValue)}'`
        //     }else{
        //         let targetValue = {
        //             "datasource" : {
        //                 "target_variable_settings":{
        //                 }
        //             }
        //         }
        //         let includedColumns = filters?.target_variable_settings;
        //         includedColumns?.forEach((res)=>{
        //             const result = {
        //                 [res]: {}
        //             };
        //             targetValue.datasource.target_variable_settings = {
        //                 ...targetValue.datasource.target_variable_settings,
        //                 ...result 
        //             };
        //         })
        //         whereClause += ` AND settings @> '${JSON.stringify(targetValue)}'`
        //     }
        // }

        // // filter query for feature filters 
        // if(filters?.feature_filters){
        //     let exclude_features = {
        //         "datasource" : {
        //             "exclude_features":{
        //             }
        //         }
        //     }
        //     Object.keys(filters?.feature_filters).forEach((column)=>{
        //         let columnData = filters?.feature_filters[column]
        //         const operatorAndTargetValue = getOperator(columnData?.operator,columnData?.value)
        //         const result = {
        //             [column]: operatorAndTargetValue ? operatorAndTargetValue : {}
        //         };
        //         exclude_features.datasource.exclude_features[column] = result[column];
        //     })
        //     whereClause += ` AND settings @> '${JSON.stringify(exclude_features)}'`
        // }

        // // filter on excluded features
        // if (filters?.exclude_features && filters?.exclude_features.length > 0 && filters?.exclude_features[0] != '') {
        //     whereClause = `
        //     EXISTS (
        //         SELECT 1
        //         FROM jsonb_array_elements_text((golden_value_data.settings -> 'datasource' -> 'exclude_features')::jsonb) AS elem
        //         WHERE elem NOT IN (${filters?.exclude_features.map(feature => `'${feature}'`).join(', ')})
        //         )
        //         AND ${whereClause}
        //         `;
        // }
        let totalCount = await GoldenValueData.count({
            where: sequelize.literal(whereClause),
          });

          let goldenValueData = await GoldenValueData.findAll({
            where: sequelize.literal(whereClause),
            offset: (page - 1) * pageSize,
            include: [
                {
                    model: workflowFilters, 
                    as: 'workflowFilter', 
                    attributes: ['id', 'name'], 
                }
            ],
            order: [['created_at', 'DESC']]
            // attributes: [
            //     'id',
            //     'user_id',
            //     'name',
            //     'workflow_id',
            //     'created_at',
            //     'golden_value',
            //     'settings'
            // ],
        });

        // Apply filters if they exist
        // if (filters?.exclude_features?.length > 0) {
        //     goldenValueData = goldenValueData.filter(data => {
        //         const dataExcludeFeatures = data.dataValues.settings?.datasource?.exclude_features || [];
        //         // Check if all filter features exist in the data's exclude_features
        //         return filters.exclude_features.every(feature => 
        //             dataExcludeFeatures.includes(feature)
        //         );
        //     });
        // }

        const minMaxResults = goldenValueData.map((data) => {
            // Use reduce to find the min and max for each data entry's golden_value array
            const minMax = data.dataValues.golden_value.reduce(
                (acc, curr) => {
                    if (curr.golden_value < acc.min) acc.min = curr.golden_value;
                    if (curr.golden_value > acc.max) acc.max = curr.golden_value;
                    return acc;
                },
                { min: Infinity, max: -Infinity }
            );
        
            // Adding the calculated min and max to each item
            data.dataValues.min = minMax.min;
            data.dataValues.max = minMax.max;
        
            return data;
        });

        return res.status(200).json({
            message: 'Golden value Data fetched successfully',
            data: minMaxResults,
            totalCount:totalCount,
            page:page
        });
    } catch (error) {
        console.error('Error fetching golden value data:', error);
        return res.status(500).json({ error: 'Failed to fetch data' });
    }
};

const getOperator = (operator, value) => {

    // Convert value to number if possible
    if(!value || value ==',') value = undefined   // when we were sending '' value then it was converted to 0 i.e why we have set it to undefined to remove value key
    const valueConverted = isNaN(Number(value)) ? value : Number(value);

    // Helper function to split and convert to number if possible
    const splitAndConvert = (value) => {
        if (value && isNaN(Number(value))){
            let data=  value.split(',').map(val => isNaN(Number(val)) ? val : Number(val));
            return data
        } 
        else return null;
    };

    // Prepare the base result
    const operatorMap = {
        '=': { value: valueConverted, operator: '=' },
        '!=': { value: valueConverted, operator: '!=' },
        '>': { value: valueConverted, operator: '>' },
        '<': { value: valueConverted, operator: '<' },
        '>=': { value: valueConverted, operator: '>=' },
        '<=': { value: valueConverted, operator: '<=' },
        'null': { operator: 'null' },
        'notNull': { operator: 'notNull' },
        'in': { value: valueConverted, operator: 'in' },
        'contains': { value: valueConverted, operator: 'in' },
        'notIn': { value: valueConverted, operator: 'notIn' },
        'between': {
            value: splitAndConvert(valueConverted),  // Split and convert if necessary
            operator: 'between'
        },
        'notBetween': {
            value: splitAndConvert(valueConverted),  // Split and convert if necessary
            operator: 'notBetween'
        },
        'beginsWith': { value: valueConverted, operator: 'beginsWith' },
        'endsWith': { value: valueConverted, operator: 'endsWith' },
        'doesNotBeginWith': { value: valueConverted, operator: 'doesNotBeginWith' },
        'doesNotEndWith': { value: valueConverted, operator: 'doesNotEndWith' },
        'doesNotContain': { value: valueConverted, operator: 'doesNotContain' }
    };

    // If the value is empty, remove the 'value' key from the object
    const result = operatorMap[operator] || null;
    if (result && (result.value === '' || result.value === null || result.value === undefined)) {
        delete result.value;
    }

    return result;
};

export const updateGoldenValueName = async (req, res) => {
    const { id } = req.params;
    const { name } = req.body;

    try {
        if (!name) {
            return res.status(400).json({ error: 'Name is required' });
        }

        const goldenValue = await GoldenValueData.findOne({
            where: { 
                id,
                // user_id: req.user.id 
            }
        });

        if (!goldenValue) {
            return res.status(404).json({ error: 'Golden value not found' });
        }

        goldenValue.name = name;
        await goldenValue.save();

        return res.status(200).json({
            message: 'Golden value name updated successfully',
            data: goldenValue
        });
    } catch (error) {
        console.error('Error updating golden value name:', error);
        return res.status(500).json({ error: 'Failed to update golden value name' });
    }
};

export const deleteGoldenValue = async (req, res) => {
    const { id } = req.params;

    try {
        const goldenValue = await GoldenValueData.findOne({
            where: { 
                id,
                user_id: req.user.id 
            }
        });

        if (!goldenValue) {
            return res.status(404).json({ error: 'Golden value not found' });
        }

        await goldenValue.destroy();

        return res.status(200).json({
            message: 'Golden value deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting golden value:', error);
        return res.status(500).json({ error: 'Failed to delete golden value' });
    }
};
function compareObjects(obj1, obj2) {
    // Helper function to compare two values
    function compareValues(val1, val2) {
        // If values are objects, we recursively compare them
        if (typeof val1 === 'object' && val1 !== null && typeof val2 === 'object' && val2 !== null) {
            return compareObjects(val1, val2);
        }

        // If values are arrays, we compare them element by element
        if (Array.isArray(val1) && Array.isArray(val2)) {
            if (val1.length !== val2.length) return false;
            for (let i = 0; i < val1.length; i++) {
                if (!compareValues(val1[i], val2[i])) return false;
            }
            return true;
        }

        // Direct comparison for primitive values
        return val1 === val2;
    }

    // Check if both objects have the same keys
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) return false;

    // Compare each key and value in both objects
    for (const key of keys1) {
        if (!keys2.includes(key)) {
            console.log(`Key "${key}" is not matching in second object.`);
            return false; // If a key is missing in one of the objects, return false
        }

        if (!compareValues(obj1[key], obj2[key])) {
            console.log(`Mismatch at key "${key}".`);
            return false; // If values don't match, return false
        }
    }

    // If all checks pass
    return true;
}


export const compareGoldenValues = async (req , res) =>{
    try {

        const { ids } = req.query;  
        const idArray = ids.split(',');

        const goldenValues = await GoldenValueData.findAll({
            where: {
                id: idArray,
                user_id: req.user.id
            }
        });

        if (goldenValues.length === 0) {
            return generateResponse(req, 404, "Golden values not found ")
        }

        // Step 1: Collect all distinct keys from all the golden_value arrays
        const distinctKeys = new Set();

        // Extract all keys from each golden_value array
        const columns = [{
            id : 0,
            name:'Columns',
            key : `key` 
        }]
        goldenValues.forEach(goldenValue => {
            let column={
                id : goldenValue.dataValues.id,
                name:goldenValue.dataValues.name,
                key : `golden_value_${goldenValue.dataValues.id}` 
            }
            columns.push(column)
            goldenValue.dataValues.golden_value.forEach((item) => {
                distinctKeys.add(item.key);
            });
        });

        // Step 2: Build the result structure
        const result = [];
        
        distinctKeys.forEach((key) => {
            const keyData = { key };
            goldenValues.forEach((goldenValue, index) => {
                const goldenValueData = goldenValue.dataValues.golden_value;
                const matchingItem = goldenValueData.find((item) => item.key === key);
                keyData[`golden_value_${goldenValue.dataValues.id}`] = matchingItem ? matchingItem.golden_value : 0;
            });

            result.push(keyData);
        });
        const response={
            goldenValues:result,
            columns:columns
        }
        return generateResponse(res, 200, 'File uploaded successfully.', response)
    } catch (error) {
    return generateResponse(req, 500, "Internal server error ")
        
    } 

}

