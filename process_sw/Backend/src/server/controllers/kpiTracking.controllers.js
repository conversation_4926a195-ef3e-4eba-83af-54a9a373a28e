import { generateResponse } from "../utils/commonResponse.js";
import KpiTrackingMongo from "../models/kpiTracking.mongo.model.js";
import Systems from "../models/systems.model.js";

const mapOperatorToMongo = (operator, value) => {
  // Helper function to parse values to numbers if possible
  const parseValue = (val) => {
    const num = parseFloat(val);
    return isNaN(num) ? val : num;
  };

  let parsedValue;
  if (Array.isArray(value)) {
    parsedValue = value.map((v) => parseValue(v));
  } else {
    parsedValue = parseValue(value);
  }

  switch (operator) {
    case "=":
      return parsedValue;
    case "!=":
      return { $ne: parsedValue };
    case "<":
      return { $lt: parsedValue };
    case "<=":
      return { $lte: parsedValue };
    case ">":
      return { $gt: parsedValue };
    case ">=":
      return { $gte: parsedValue };
    case "between":
      if (Array.isArray(parsedValue) && parsedValue.length === 2) {
        return { $gte: parsedValue[0], $lte: parsedValue[1] };
      }
      throw new Error("Between operator requires an array of two values");
    case "notBetween":
      if (Array.isArray(parsedValue) && parsedValue.length === 2) {
        return { $lt: parsedValue[0], $gt: parsedValue[1] };
      }
      throw new Error("notBetween operator requires an array of two values");
    default:
      return parsedValue;
  }
};

// For table data with pagination
export const getKpiTrackingData = async (req, res) => {
  const tenantId = req.user.tenant_id;
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 10;
  const startDate = req.query.startDate;
  const endDate = req.query.endDate;
  const genericFilters = req.query.genericFilters
    ? JSON.parse(req.query.genericFilters)
    : null;

  try {
    const query = { tenantId: parseInt(tenantId) };

    // Date range filter
    if (startDate || endDate) {
      query.datetime = {};
      if (startDate) query.datetime.$gte = new Date(startDate);
      if (endDate) query.datetime.$lte = new Date(endDate);
    }

    // Generic filters
    if (genericFilters && genericFilters.length > 0) {
      genericFilters.forEach((filter) => {
        const { field, operator, value } = filter;
        if (field && operator && value !== undefined) {
          query[field] = mapOperatorToMongo(operator, value);
        }
      });
    }

    const skip = (page - 1) * pageSize;
    const limit = pageSize;

    const [data, count] = await Promise.all([
      KpiTrackingMongo.find(query)
        .sort({ datetime: -1 })
        .skip(skip)
        .limit(limit),
      KpiTrackingMongo.countDocuments(query),
    ]);
    // Extract unique system_ids from MongoDB data
    const systemIds = [
      ...new Set(data.map((d) => d.system_id).filter(Boolean)),
    ];

    // Fetch system names from PostgreSQL
    let systemNamesMap = {};
    if (systemIds.length > 0) {
      const systems = await Systems.findAll({
        where: { id: systemIds },
        attributes: ["id", "name"],
      });

      // Create a map of system_id -> system_name
      systemNamesMap = systems.reduce((acc, system) => {
        acc[system.id] = system.name;
        return acc;
      }, {});
    }
    let kpiData = [...data];
    const enrichedData = kpiData.map((record) => ({
      ...record.toObject(),
      name: systemNamesMap[record.system_id] || null, // Assign name or null if not found
    }));
    return generateResponse(
      res,
      200,
      "KPI tracking data fetched successfully",
      {
        data: enrichedData,
        total: count,
        page,
        pageSize,
      }
    );
  } catch (error) {
    console.error("Error fetching KPI tracking data:", error);
    return generateResponse(res, 500, "Internal server error");
  }
};

// For graph data with pre-calculated averages and statistics
export const getKpiTrackingGraphData = async (req, res) => {
  const tenantId = req.user.tenant_id;
  const startDate = req.query.startDate;
  const endDate = req.query.endDate;
  const groupBy = req.query.groupBy || "daily";
  const genericFilters = req.query.genericFilters
    ? JSON.parse(req.query.genericFilters)
    : null;

  try {
    if (tenantId !== 1 && tenantId !== 2) {
      return generateResponse(res, 200, "No data available for this tenant", {
        data: [],
      });
    }

    // Select the correct field based on tenantId
    const measurementField = tenantId === 1 ? "ph" : "rerun_iodine";

    const query = { tenantId: parseInt(tenantId) };

    // Apply date filters
    if (startDate || endDate) {
      query.datetime = {};
      if (startDate) query.datetime.$gte = new Date(startDate);
      if (endDate) query.datetime.$lte = new Date(endDate);
    }

    // Apply generic filters
    if (genericFilters && genericFilters.length > 0) {
      genericFilters.forEach((filter) => {
        const { field, operator, value } = filter;
        if (field && operator && value !== undefined) {
          query[field] = mapOperatorToMongo(operator, value);
        }
      });
    }

    // Define group format based on selected granularity
    const groupByFormat =
      {
        daily: "%Y-%m-%d",
        weekly: "%Y-%V",
        monthly: "%Y-%m",
      }[groupBy] || "%Y-%m-%d";

    let aggregationPipeline = [
      {
        $match: {
          ...query,
          datetime: { $gte: new Date(startDate), $lte: new Date(endDate) },
        },
      },
      {
        $addFields: {
          dateGroup: {
            $dateToString: { format: groupByFormat, date: "$datetime" },
          },
          numericValue: { $toDouble: `$${measurementField}` }, // Ensure numeric conversion
        },
      },
      {
        $group: {
          _id: "$dateGroup",
          mean_value: { $avg: "$numericValue" },
          std_dev_value: { $stdDevPop: "$numericValue" },
          count: { $sum: 1 },
          values: { $push: "$numericValue" }, // Store values for median calculation
        },
      },
      {
        $addFields: {
          median_value: {
            $let: {
              vars: {
                sortedValues: { $sortArray: { input: "$values", sortBy: 1 } },
              },
              in: {
                $cond: {
                  if: { $gt: [{ $size: "$$sortedValues" }, 0] },
                  then: {
                    $arrayElemAt: [
                      "$$sortedValues",
                      { $floor: { $divide: [{ $size: "$$sortedValues" }, 2] } },
                    ],
                  },
                  else: null,
                },
              },
            },
          },
        },
      },
      { $project: { values: 0 } }, // Remove unnecessary array
      { $sort: { _id: 1 } },
    ];
    if (groupBy == "weekly") aggregationPipeline.push({ $skip: 1 });

    const results = await KpiTrackingMongo.aggregate(aggregationPipeline);

    const formattedResults = results.map((result) => ({
      date: result._id,
      mean_value:
        result.mean_value !== null
          ? parseFloat(result.mean_value.toFixed(2))
          : null,
      median_value:
        result.median_value !== null
          ? parseFloat(result.median_value.toFixed(2))
          : null,
      std_dev_value:
        result.std_dev_value !== null
          ? parseFloat(result.std_dev_value.toFixed(2))
          : null,
      count: result.count,
    }));

    return generateResponse(res, 200, "Graph data fetched successfully", {
      data: formattedResults,
    });
  } catch (error) {
    console.error("Error fetching graph data:", error);
    return generateResponse(res, 500, "Internal server error");
  }
};
