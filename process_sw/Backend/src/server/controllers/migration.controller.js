import KpiTrackingMongo from "../models/kpiTracking.mongo.model.js";
import KpiTracking from "../models/kpiTracking.model.js";
import KpiTrackingCarbon from "../models/kpiTrackingCarbon.model.js";
import { sequelize } from "../database/dbConnection.js";
import mongoose from "mongoose";

export const migrateData = async (req, res) => {
  try {
    console.log("Fetching PostgreSQL data...");

    // Fetch data using Sequelize models
    const kpiTrackingRows = await KpiTracking.findAll({ raw: true });
    const kpiTrackingCarbonRows = await KpiTrackingCarbon.findAll({ raw: true });

    // Transform data (map fields dynamically at the root level)
    const formattedData = [...kpiTrackingRows, ...kpiTrackingCarbonRows].map(
      (row) => {
        return {
          id: row.id,
          tenantId: row.tenant_id,
          systemId: row.system_id,
          datetime: row.datetime,
          ...row, // Spread remaining fields dynamically at the root level
        };
      }
    );

    // Insert into MongoDB
    console.log("Inserting data into MongoDB...");
    await KpiTrackingMongo.insertMany(formattedData);

    console.log("Migration successful!");
    res.status(200).send("Migration successful!");
  } catch (error) {
    console.error("Migration failed:", error);
    res.status(500).send("Migration failed");
  } finally {
    await sequelize.close();
    await mongoose.disconnect();
  }
}; 