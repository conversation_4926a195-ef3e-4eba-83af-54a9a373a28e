import Pannel from "../models/pannel.model.js";
import CSVFilesSchema from "../models/file.model.js";
import User from "../models/user.model.js";

// Create a new pannel
export const createPannel = async (req, res) => {
    const { name, configurations, data_source_path, data_source } = req.body;

    try {
        const newPannel = await Pannel.create({
            name,
            configurations,
            data_source_path,
            data_source: data_source || 'batch',
            user_id: req.user.id
        });

        return res.status(201).json({
            status: 201,
            message: "Pannel created successfully.",
            data: newPannel
        });
    } catch (error) {
        console.error('Error creating pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to create pannel' });
    }
};

// Get all pannels for a user
export const getPannels = async (req, res) => {
    try {
        const pannels = await Pannel.findAll({
            where: { user_id: req.user.id },
            include: [
                {
                    model: CSVFilesSchema,
                    as: 'csvFile',
                    attributes: ['id', 'file_name', 'file_path']
                }
            ],
            order: [['updated_at', 'DESC']]
        });

        return res.status(200).json({
            status: 200,
            message: "Pannels fetched successfully.",
            data: pannels
        });
    } catch (error) {
        console.error('Error fetching pannels:', error);
        return res.status(500).json({ status: 500, error: 'Failed to fetch pannels' });
    }
};

// Get a specific pannel by ID
export const getPannelById = async (req, res) => {
    const { id } = req.params;

    try {
        const pannel = await Pannel.findOne({
            where: {
                id: id,
                user_id: req.user.id
            },
            include: [
                {
                    model: CSVFilesSchema,
                    as: 'csvFile',
                    attributes: ['id', 'file_name', 'file_path']
                }
            ]
        });

        if (!pannel) {
            return res.status(404).json({
                status: 404,
                error: 'Pannel not found'
            });
        }

        return res.status(200).json({
            status: 200,
            message: "Pannel fetched successfully.",
            data: pannel
        });
    } catch (error) {
        console.error('Error fetching pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to fetch pannel' });
    }
};

// Update a pannel
export const updatePannel = async (req, res) => {
    const { id } = req.params;
    const { name, configurations, data_source_path, data_source } = req.body;

    try {
        const pannel = await Pannel.findOne({
            where: {
                id: id,
                user_id: req.user.id
            }
        });

        if (!pannel) {
            return res.status(404).json({
                status: 404,
                error: 'Pannel not found'
            });
        }

        // Update the pannel
        await pannel.update({
            name: name || pannel.name,
            configurations: configurations || pannel.configurations,
            data_source_path: data_source_path !== undefined ? data_source_path : pannel.data_source_path,
            data_source: data_source || pannel.data_source,
            updated_at: new Date()
        });

        return res.status(200).json({
            status: 200,
            message: "Pannel updated successfully.",
            data: pannel
        });
    } catch (error) {
        console.error('Error updating pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to update pannel' });
    }
};

// Delete a pannel
export const deletePannel = async (req, res) => {
    const { id } = req.params;

    try {
        const pannel = await Pannel.findOne({
            where: {
                id: id,
                user_id: req.user.id
            }
        });

        if (!pannel) {
            return res.status(404).json({
                status: 404,
                error: 'Pannel not found'
            });
        }

        await pannel.destroy();

        return res.status(200).json({
            status: 200,
            message: "Pannel deleted successfully."
        });
    } catch (error) {
        console.error('Error deleting pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to delete pannel' });
    }
};

// Save pannel from current panel state (similar to individual panel saving)
export const savePannelFromState = async (req, res) => {
    const {
        name,
        panelType,
        configuration,
        csvFileId,
        position,
        panelId,
        title
    } = req.body;

    try {
        // Create configurations object with all panel data
        const configurations = {
            panelType: panelType,
            title: title || name || 'Panel',
            position: position || { x: 0, y: 0, w: 6, h: 6 },
            panelId: panelId,
            csvFileId: csvFileId,
            ...configuration
        };

        const pannel = await Pannel.create({
            name: name || `${panelType} Panel`,
            configurations: configurations,
            data_source_path: csvFileId,
            data_source: 'batch',
            user_id: req.user.id
        });

        return res.status(201).json({
            status: 201,
            message: "Pannel saved successfully.",
            data: pannel
        });
    } catch (error) {
        console.error('Error saving pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to save pannel' });
    }
};

// Get pannels by data source (CSV file)
export const getPannelsByDataSource = async (req, res) => {
    const { csvFileId } = req.params;

    try {
        const pannels = await Pannel.findAll({
            where: {
                user_id: req.user.id,
                data_source_path: csvFileId
            },
            include: [
                {
                    model: CSVFilesSchema,
                    as: 'csvFile',
                    attributes: ['id', 'file_name', 'file_path']
                }
            ],
            order: [['updated_at', 'DESC']]
        });

        return res.status(200).json({
            status: 200,
            message: "Pannels fetched successfully.",
            data: pannels
        });
    } catch (error) {
        console.error('Error fetching pannels by data source:', error);
        return res.status(500).json({ status: 500, error: 'Failed to fetch pannels' });
    }
};

// Get pannels by viewId (for compatibility with frontend expecting viewId=0)
export const getPannelsByViewId = async (req, res) => {
    const { viewId } = req.params;

    try {
        // For viewId=0, return all pannels for the user (simulating latest view behavior)
        if (viewId === '0') {
            const pannels = await Pannel.findAll({
                where: { user_id: req.user.id },
                include: [
                    {
                        model: CSVFilesSchema,
                        as: 'csvFile',
                        attributes: ['id', 'file_name', 'file_path']
                    }
                ],
                order: [['updated_at', 'DESC']]
            });

            // Transform pannels data to match expected view structure
            const transformedData = {
                id: 0, // Virtual view ID
                name: 'Pannel View',
                structure: pannels.map((pannel, index) => ({
                    i: pannel.configurations?.panelId || `panel-${pannel.id}`,
                    x: pannel.configurations?.position?.x || (index % 2) * 6,
                    y: pannel.configurations?.position?.y || Math.floor(index / 2) * 6,
                    w: pannel.configurations?.position?.w || 6,
                    h: pannel.configurations?.position?.h || 6,
                    panelType: pannel.configurations?.panelType || 'TimeSeriesPanel'
                })),
                csvfile_id: pannels.length > 0 ? pannels[0].data_source_path : null,
                viewPanels: pannels.map(pannel => ({
                    id: pannel.id,
                    views_id: 0,
                    configuration: pannel.configurations,
                    pannel_type_id: getPanelTypeId(pannel.configurations?.panelType),
                    created_at: pannel.created_at,
                    updated_at: pannel.updated_at
                }))
            };

            return res.status(200).json({
                message: 'Pannels fetched successfully',
                data: transformedData
            });
        }

        // For other viewIds, return empty or specific pannel if needed
        return res.status(404).json({ error: 'View not found' });
    } catch (error) {
        console.error('Error fetching pannels by viewId:', error);
        return res.status(500).json({ error: 'Failed to fetch pannels' });
    }
};

// Save individual panel (replaces view panel save functionality)
export const saveIndividualPannel = async (req, res) => {
    const { panelId, panelType, configuration, position, title, csvFileId } = req.body;

    try {
        // Check if panel already exists by panelId
        const existingPannel = await Pannel.findOne({
            where: {
                user_id: req.user.id,
                'configurations.panelId': panelId
            }
        });

        const pannelData = {
            name: title || `${panelType} Panel`,
            configurations: {
                panelType: panelType,
                title: title || 'Panel',
                position: position || { x: 0, y: 0, w: 6, h: 6 },
                panelId: panelId,
                csvFileId: csvFileId,
                ...configuration
            },
            data_source_path: csvFileId,
            data_source: 'batch',
            user_id: req.user.id
        };

        let savedPannel;
        if (existingPannel) {
            // Update existing panel
            await existingPannel.update(pannelData);
            savedPannel = existingPannel;
        } else {
            // Create new panel
            savedPannel = await Pannel.create(pannelData);
        }

        return res.status(200).json({
            status: 200,
            message: 'Panel saved successfully',
            data: {
                viewId: 0, // Virtual view ID
                panelId: panelId,
                panelType: panelType,
                dbPanelId: savedPannel.id,
                csvFileId: csvFileId
            }
        });
    } catch (error) {
        console.error('Error saving individual panel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to save panel' });
    }
};

// Remove individual panel (replaces view panel remove functionality)
export const removeIndividualPannel = async (req, res) => {
    const { panelId } = req.params;

    try {
        // Find panel by panelId in configurations
        const pannel = await Pannel.findOne({
            where: {
                user_id: req.user.id,
                'configurations.panelId': panelId
            }
        });

        if (!pannel) {
            return res.status(404).json({
                status: 404,
                error: 'Panel not found'
            });
        }

        await pannel.destroy();

        return res.status(200).json({
            status: 200,
            message: 'Panel removed successfully'
        });
    } catch (error) {
        console.error('Error removing individual panel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to remove panel' });
    }
};

// Helper function to get panel type ID
const getPanelTypeId = (panelType) => {
    switch (panelType) {
        case 'TimeSeriesPanel': return 1;
        case 'OverviewPanel': return 2;
        case 'HistogramPanel': return 3;
        case 'DataTablePanel': return 4;
        default: return 1;
    }
};
