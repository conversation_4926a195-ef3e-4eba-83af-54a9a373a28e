import Pannel from "../models/pannel.model.js";
import CSVFilesSchema from "../models/file.model.js";
import User from "../models/user.model.js";

// Create a new pannel
export const createPannel = async (req, res) => {
    const { name, configurations, data_source_path, data_source } = req.body;

    try {
        const newPannel = await Pannel.create({
            name,
            configurations,
            data_source_path,
            data_source: data_source || 'batch',
            user_id: req.user.id
        });

        return res.status(201).json({
            status: 201,
            message: "Pannel created successfully.",
            data: newPannel
        });
    } catch (error) {
        console.error('Error creating pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to create pannel' });
    }
};

// Get all pannels for a user
export const getPannels = async (req, res) => {
    try {
        const pannels = await Pannel.findAll({
            where: { user_id: req.user.id },
            include: [
                {
                    model: CSVFilesSchema,
                    as: 'csvFile',
                    attributes: ['id', 'file_name', 'file_path']
                }
            ],
            order: [['updated_at', 'DESC']]
        });

        return res.status(200).json({
            status: 200,
            message: "Pannels fetched successfully.",
            data: pannels
        });
    } catch (error) {
        console.error('Error fetching pannels:', error);
        return res.status(500).json({ status: 500, error: 'Failed to fetch pannels' });
    }
};

// Get a specific pannel by ID
export const getPannelById = async (req, res) => {
    const { id } = req.params;

    try {
        const pannel = await Pannel.findOne({
            where: { 
                id: id,
                user_id: req.user.id 
            },
            include: [
                {
                    model: CSVFilesSchema,
                    as: 'csvFile',
                    attributes: ['id', 'file_name', 'file_path']
                }
            ]
        });

        if (!pannel) {
            return res.status(404).json({
                status: 404,
                error: 'Pannel not found'
            });
        }

        return res.status(200).json({
            status: 200,
            message: "Pannel fetched successfully.",
            data: pannel
        });
    } catch (error) {
        console.error('Error fetching pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to fetch pannel' });
    }
};

// Update a pannel
export const updatePannel = async (req, res) => {
    const { id } = req.params;
    const { name, configurations, data_source_path, data_source } = req.body;

    try {
        const pannel = await Pannel.findOne({
            where: { 
                id: id,
                user_id: req.user.id 
            }
        });

        if (!pannel) {
            return res.status(404).json({
                status: 404,
                error: 'Pannel not found'
            });
        }

        // Update the pannel
        await pannel.update({
            name: name || pannel.name,
            configurations: configurations || pannel.configurations,
            data_source_path: data_source_path !== undefined ? data_source_path : pannel.data_source_path,
            data_source: data_source || pannel.data_source,
            updated_at: new Date()
        });

        return res.status(200).json({
            status: 200,
            message: "Pannel updated successfully.",
            data: pannel
        });
    } catch (error) {
        console.error('Error updating pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to update pannel' });
    }
};

// Delete a pannel
export const deletePannel = async (req, res) => {
    const { id } = req.params;

    try {
        const pannel = await Pannel.findOne({
            where: { 
                id: id,
                user_id: req.user.id 
            }
        });

        if (!pannel) {
            return res.status(404).json({
                status: 404,
                error: 'Pannel not found'
            });
        }

        await pannel.destroy();

        return res.status(200).json({
            status: 200,
            message: "Pannel deleted successfully."
        });
    } catch (error) {
        console.error('Error deleting pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to delete pannel' });
    }
};

// Save pannel from current panel state (similar to individual panel saving)
export const savePannelFromState = async (req, res) => {
    const { 
        name, 
        panelType, 
        configuration, 
        csvFileId, 
        position,
        panelId,
        title
    } = req.body;

    try {
        // Create configurations object with all panel data
        const configurations = {
            panelType: panelType,
            title: title || name || 'Panel',
            position: position || { x: 0, y: 0, w: 6, h: 6 },
            panelId: panelId,
            csvFileId: csvFileId,
            ...configuration
        };

        const pannel = await Pannel.create({
            name: name || `${panelType} Panel`,
            configurations: configurations,
            data_source_path: csvFileId,
            data_source: 'batch',
            user_id: req.user.id
        });

        return res.status(201).json({
            status: 201,
            message: "Pannel saved successfully.",
            data: pannel
        });
    } catch (error) {
        console.error('Error saving pannel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to save pannel' });
    }
};

// Get pannels by data source (CSV file)
export const getPannelsByDataSource = async (req, res) => {
    const { csvFileId } = req.params;

    try {
        const pannels = await Pannel.findAll({
            where: { 
                user_id: req.user.id,
                data_source_path: csvFileId
            },
            include: [
                {
                    model: CSVFilesSchema,
                    as: 'csvFile',
                    attributes: ['id', 'file_name', 'file_path']
                }
            ],
            order: [['updated_at', 'DESC']]
        });

        return res.status(200).json({
            status: 200,
            message: "Pannels fetched successfully.",
            data: pannels
        });
    } catch (error) {
        console.error('Error fetching pannels by data source:', error);
        return res.status(500).json({ status: 500, error: 'Failed to fetch pannels' });
    }
};
