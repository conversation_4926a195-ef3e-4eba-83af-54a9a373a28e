import View from "../models/view.model.js";
import Folder from "../models/folder.model.js";
import PannelType from "../models/pannelType.model.js";
import CSVFilesSchema from "../models/file.model.js";
import ViewPannel from "../models/viewPannel.model.js";
import fetch from 'node-fetch';

export const createView = async (req, res) => {
    const { name, folder_id, csvfile_id, structure, panels } = req.body;

    try {
        // Check if user already has a view - if yes, update it instead of creating new
        const existingView = await View.findOne({
            where: { user_id: req.user.id },
            order: [['updated_at', 'DESC']]
        });

        if (existingView) {
            // Update existing view instead of creating new
            await existingView.update({
                name: name || existingView.name,
                folder_id: folder_id !== undefined ? folder_id : existingView.folder_id,
                csvfile_id: csvfile_id !== undefined ? csvfile_id : existingView.csvfile_id,
                structure: structure || existingView.structure,
                updated_at: new Date()
            });

            // Update panels if provided
            if (panels && Array.isArray(panels)) {
                await ViewPannel.destroy({
                    where: { views_id: existingView.id }
                });

                for (const panel of panels) {
                    await ViewPannel.create({
                        views_id: existingView.id,
                        pannel_type_id: panel.pannel_type_id,
                        configuration: panel.configuration
                    });
                }
            }

            return res.status(200).json({
                status: 200,
                message: "View updated successfully.",
                data: existingView
            });
        }

        // Create new view only if none exists
        const newView = await View.create({
            user_id: req.user.id,
            folder_id,
            name,
            structure: structure || {},
            csvfile_id
        });

        // Create panels if provided
        if (panels && Array.isArray(panels)) {
            for (const panel of panels) {
                await ViewPannel.create({
                    views_id: newView.id,
                    pannel_type_id: panel.pannel_type_id,
                    configuration: panel.configuration
                });
            }
        }

        return res.status(201).json({
            status: 201,
            message: "View created successfully.",
            data: newView
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({ status: 500, error: 'Failed to create view' });
    }
};

export const deleteView = async (req, res) => {
    const { id, type } = req.params;

    try {
        if(type == 'file'){
            const view = await View.findByPk(id);

            if (!view) {
                return res.status(404).json({ error: 'View not found' });
            }
            // const result = await MLJob.deleteMany({ workflow_id: id });
            // const mljobdata = await MLJobData.deleteMany({ workflow_id: id });
    
            await view.destroy();
    
            return res.status(200).json({ message: 'View deleted successfully' });
        }
        else if(type == 'folder') {
            const folder = await Folder.findByPk(id);

            if (!folder) {
                return res.status(404).json({ error: 'Folder not found' });
            }
    
            await folder.destroy();
    
            return res.status(200).json({ message: 'Folder deleted successfully' });
        }

    } catch (error) {
        console.error('Error deleting View:', error);
        return res.status(500).json({ error: 'Failed to delete' });
    }
};


export const getViewById = async (req, res) => {
    const { viewId } = req.params;

    try {
        // If viewId is 0, get the latest view for the user
        if (viewId === '0') {
            const latestView = await View.findOne({
                where: { user_id: req.user.id },
                attributes: ['id', 'name', 'structure', 'csvfile_id'],
                include: [
                    {
                        model: ViewPannel,
                        as: 'viewPanels',
                        required: false,
                    },
                ],
                // order: [['updated_at', 'DESC']]
            });

            if (!latestView) {
                // If no view exists, create a default one
                const defaultView = await View.create({
                    user_id: req.user.id,
                    name: 'Default View',
                    structure: {},
                    csvfile_id: null
                });

                return res.status(200).json({ 
                    message: 'Default view created successfully', 
                    data: defaultView 
                });
            }

            return res.status(200).json({ 
                message: 'Latest view fetched successfully', 
                data: latestView 
            });
        }

        const view = await View.findOne({
            where: { id : viewId },
            attributes: ['id', 'name', 'structure', 'csvfile_id'], 
            include: [
                {
                    model: ViewPannel,
                    as: 'viewPanels',
                    required: false, // Allows workflows without components
                },
            ]
        });
        if (!view) {
            return res.status(404).json({ error: 'View not found' });
        }

        return res.status(200).json({ message: 'View fetched successfully' , data: view });
    } catch (error) {
        console.error('Error fetching view by id:', error);
        return res.status(500).json({ error: 'Failed to fetch view' });
    }
};

export const updateView = async (req, res) => {
    const { viewId } = req.params;
    const { view: viewData, panel: panelData } = req.body;

    try {
        // If viewId is 0, use the latest view for the user
        let view;
        if (viewId === '0') {
            view = await View.findOne({
                where: { user_id: req.user.id },
                // order: [['updated_at', 'DESC']]
            });
            
            // If no view exists, create one
            if (!view) {
                view = await View.create({
                    user_id: req.user.id,
                    name: 'Default View',
                    structure: {},
                    csvfile_id: null
                });
            }
        } else {
            view = await View.findOne({
                where: { id : viewId }
            });
        }

        if (!view) {
            return res.status(404).json({ error: 'View not found' });
        }

        // Update the view structure and file ID
        if (viewData) {
            if (viewData.structure) {
                view.structure = viewData.structure;
            }
            if (viewData.csvfile_id) {
                view.csvfile_id = viewData.csvfile_id;
            }
            view.updated_at = new Date();
            await view.save();
        }

        // Update or create panels
        if (panelData && Array.isArray(panelData)) {
            // Delete existing panels for this view
            await ViewPannel.destroy({
                where: { views_id: view.id }
            });

            // Create new panels
            for (const panel of panelData) {
                await ViewPannel.create({
                    views_id: view.id,
                    pannel_type_id: panel.panel_type_id,
                    configuration: panel.configuration
                });
            }
        }

        return res.status(200).json({
            status: 200,
            message: 'View updated successfully',
            data: {
                id: view.id,
                name: view.name,
                structure: view.structure
            }
        });
    } catch (error) {
        console.error('Error updating view:', error);
        return res.status(500).json({ status: 500, error: 'Failed to update view' });
    }
};


export const getPannelTypes = async (req, res) => {
    try {
        const pannelTypes = await PannelType.findAll({
            order: [['created_at', 'DESC']],
        });

        return res.status(200).json({
            message: "pannel Types fetched successfully.",
            data: pannelTypes
             });
    } catch (error) {
        console.error('Error fetching pannel types:', error);
        return res.status(500).json({ error: 'Failed to fetch pannel types' });
    }
};


// Save individual panel configuration to the current view
export const saveIndividualPanel = async (req, res) => {
    const { panelId, panelType, configuration, position, title, csvFileId } = req.body;

    try {
        // Get the latest view for the user (single view pattern)
        let view = await View.findOne({
            where: { user_id: req.user.id },
            order: [['updated_at', 'DESC']]
        });

        // If no view exists, create a default one
        if (!view) {
            view = await View.create({
                user_id: req.user.id,
                name: 'Default View',
                structure: [],
                csvfile_id: csvFileId || null
            });
        } else {
            // Update the view's csvfile_id if provided
            if (csvFileId && view.csvfile_id !== csvFileId) {
                view.csvfile_id = csvFileId;
                await view.save();
            }
        }

        // Get panel type ID
        const getPanelTypeId = (type) => {
            switch (type) {
                case 'TimeSeriesPanel': return 1;
                case 'OverviewPanel': return 2;
                case 'HistogramPanel': return 3;
                case 'DataTablePanel': return 4;
                default: return 1;
            }
        };

        // Create panel data
        const panelData = {
            views_id: view.id,
            pannel_type_id: getPanelTypeId(panelType),
            configuration: {
                title: title || 'Panel',
                position: position || { x: 0, y: 0, w: 6, h: 6 },
                panelId: panelId, // Store the frontend panel ID
                csvFileId: csvFileId, // Store the CSV file ID in panel config
                ...configuration
            }
        };

        // Look for existing panel with the same panelId in configuration
        const existingPanels = await ViewPannel.findAll({
            where: { views_id: view.id }
        });

        // Find panel by panelId stored in configuration
        const existingPanel = existingPanels.find(panel => {
            const config = panel.configuration || {};
            return config.panelId === panelId;
        });

        let savedPanel;
        if (existingPanel) {
            // Update existing panel
            await existingPanel.update({
                configuration: panelData.configuration,
                updated_at: new Date()
            });
            savedPanel = existingPanel;
        } else {
            // Create new panel
            savedPanel = await ViewPannel.create(panelData);
        }

        // Update view structure for this specific panel
        if (position && panelId) {
            // Ensure currentStructure is always an array
            let currentStructure = view.structure;
            if (!Array.isArray(currentStructure)) {
                currentStructure = [];
            }
            
            // Find and update the specific panel in structure by panelId
            const panelStructureIndex = currentStructure.findIndex(item => 
                item && item.i === panelId
            );

            const structureItem = {
                i: panelId,
                x: position.x,
                y: position.y,
                w: position.w,
                h: position.h,
                panelType: panelType
            };

            if (panelStructureIndex >= 0) {
                // Update existing structure item
                currentStructure[panelStructureIndex] = structureItem;
            } else {
                // Add new structure item
                currentStructure.push(structureItem);
            }

            view.structure = currentStructure;
        }

        // Update view timestamp
        view.updated_at = new Date();
        await view.save();

        return res.status(200).json({
            status: 200,
            message: 'Panel saved successfully',
            data: {
                viewId: view.id,
                panelId: panelId,
                panelType: panelType,
                dbPanelId: savedPanel.id,
                csvFileId: csvFileId
            }
        });
    } catch (error) {
        console.error('Error saving individual panel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to save panel' });
    }
};

// Remove individual panel from the current view
export const removeIndividualPanel = async (req, res) => {
    const { panelId } = req.params;

    try {
        // Get the latest view for the user (single view pattern)
        const view = await View.findOne({
            where: { user_id: req.user.id },
            order: [['updated_at', 'DESC']]
        });

        if (!view) {
            return res.status(404).json({ error: 'No view found for user' });
        }

        // Find and remove the panel from ViewPannel table
        const existingPanels = await ViewPannel.findAll({
            where: { views_id: view.id }
        });

        // Find panel by panelId stored in configuration
        const panelToRemove = existingPanels.find(panel => {
            const config = panel.configuration || {};
            return config.panelId === panelId;
        });

        if (panelToRemove) {
            await panelToRemove.destroy();
        }

        // Remove panel from view structure
        if (view.structure) {
            // Ensure structure is an array
            let currentStructure = view.structure;
            if (!Array.isArray(currentStructure)) {
                currentStructure = [];
            }
            const updatedStructure = currentStructure.filter(item => item && item.i !== panelId);
            view.structure = updatedStructure;
        }

        // Update view timestamp
        view.updated_at = new Date();
        await view.save();

        return res.status(200).json({
            status: 200,
            message: 'Panel removed successfully',
            data: {
                viewId: view.id,
                panelId: panelId
            }
        });
    } catch (error) {
        console.error('Error removing individual panel:', error);
        return res.status(500).json({ status: 500, error: 'Failed to remove panel' });
    }
};
