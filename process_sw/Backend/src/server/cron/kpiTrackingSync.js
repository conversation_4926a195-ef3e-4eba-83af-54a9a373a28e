import cron from 'node-cron';
import pkg from 'pg';
const { Client } = pkg;
import { 
  SecretsManagerClient, 
  GetSecretValueCommand 
} from '@aws-sdk/client-secrets-manager';
import KpiTrackingMongo from '../models/kpiTracking.mongo.model.js';
import mongoose from 'mongoose';
import { connectMongoDB } from '../database/dbConnection.js';
import 'dotenv/config';

// Initialize Secrets Manager client
const secretsManagerClient = new SecretsManagerClient({ 
  region: process.env.AWS_REGION || 'eu-central-1' 
});

// Function to get secret from AWS Secrets Manager
async function getSecret() {
  try {
    // const secretName = "banasCheesePlantLabDataCredentials";
    const secretName = process.env.SECRET_NAME;
    
    const command = new GetSecretValueCommand({
      SecretId: secretName
    });
    
    const response = await secretsManagerClient.send(command);
    return JSON.parse(response.SecretString);
  } catch (error) {
    console.error('Error fetching secret:', error);
    throw error;
  }
}

// Function to connect to the 3rd party PostgreSQL database
async function connectToThirdPartyDB() {
  try {
    // Get database connection parameters from Secrets Manager
    const dbCredentials = await getSecret();
    
    // Create PostgreSQL client
    const client = new Client({
      host: dbCredentials.host,
      port: dbCredentials.port,
      database: dbCredentials.dbname,
      user: dbCredentials.username,
      password: dbCredentials.password,
      ssl: { rejectUnauthorized: false } // Adjust based on your SSL requirements
    });

    await client.connect();
    console.log('Connected to third-party PostgreSQL database');
    return client;
  } catch (error) {
    console.error('Failed to connect to third-party database:', error);
    throw error;
  }
}

// Function to get the latest datetime from MongoDB
async function getLatestDatetime() {
  try {
    const latestRecord = await KpiTrackingMongo.findOne({})
      .sort({ datetime: -1 })
      .limit(1);
    
    return latestRecord ? latestRecord.datetime : new Date('2000-01-01'); // Default date if no records
  } catch (error) {
    console.error('Error fetching latest datetime:', error);
    throw error;
  }
}

// Function to get the next available ID for MongoDB
async function getNextAvailableId() {
  try {
    // Find the document with the highest ID
    const highestRecord = await KpiTrackingMongo.findOne({})
      .sort({ id: -1 })
      .limit(1);
    
    // Return the next ID (highest + 1) or start with 1 if no records exist
    return highestRecord ? highestRecord.id + 1 : 1;
  } catch (error) {
    console.error('Error getting next available ID:', error);
    throw error;
  }
}

// Function to fetch data in chunks from PostgreSQL
async function fetchDataInChunks(pgClient, latestDatetime, chunkSize = 100, offset = 0) {
  try {
    // Query to fetch records newer than the latest datetime in our MongoDB
    // Map the 3rd party field names to our expected field names
    const query = `
      SELECT 
        "VatpH" as ph,
        "VatNo" as vat_no,
        "CreateDate" as datetime,
        recipe_no,
        sequence_no
        -- Add any other fields you need
      FROM third_party_table  -- Replace with actual table name
      WHERE "CreateDate" > $1
      ORDER BY "CreateDate" ASC
      LIMIT $2 OFFSET $3
    `;
    
    const result = await pgClient.query(query, [latestDatetime, chunkSize, offset]);
    return result.rows;
  } catch (error) {
    console.error('Error fetching data from PostgreSQL:', error);
    throw error;
  }
}

// Function to insert data into MongoDB
async function insertDataIntoMongoDB(data) {
  try {
    if (data.length === 0) return 0;
    
    // Get the next available ID
    let nextId = await getNextAvailableId();
    
    // Transform data with static values, handle missing fields, and assign incremental IDs
    const formattedData = data.map(row => {
      const formattedRow = {
        // Assign incremental ID
        id: nextId++,
        
        // Static values
        tenantId: 1,
        systemId: 2,
        tenant_id: 1,
        
        // Dynamic values from the query with null fallbacks
        datetime: row.datetime || null,
        ph: row.ph || null,
        vat_no: row.vat_no || null,
        recipe_no: row.recipe_no || null,
        sequence_no: row.sequence_no || null,
        
        // Include all other fields from the row (except id which we're overriding)
        ...Object.entries(row)
          .filter(([key]) => key !== 'id')
          .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {})
      };
      
      return formattedRow;
    });
    
    // Insert data into MongoDB
    const result = await KpiTrackingMongo.insertMany(formattedData);
    console.log(`Inserted ${result.length} records into MongoDB`);
    return result.length;
  } catch (error) {
    console.error('Error inserting data into MongoDB:', error);
    throw error;
  }
}

// Main function to sync data
async function syncKpiTrackingData() {
  let pgClient = null;
  
  try {
    // Ensure MongoDB connection
    if (mongoose.connection.readyState !== 1) {
      await connectMongoDB();
    }
    
    // Connect to PostgreSQL
    pgClient = await connectToThirdPartyDB();
    
    // Get the latest datetime from MongoDB
    const latestDatetime = await getLatestDatetime();
    console.log(`Fetching records newer than ${latestDatetime}`);
    
    let offset = 0;
    let totalInserted = 0;
    let hasMoreData = true;
    
    // Fetch and insert data in chunks
    while (hasMoreData) {
      const data = await fetchDataInChunks(pgClient, latestDatetime, 100, offset);
      
      if (data.length === 0) {
        hasMoreData = false;
        break;
      }
      
      const inserted = await insertDataIntoMongoDB(data);
      totalInserted += inserted;
      
      // Prepare for next chunk
      offset += 100;
      
      // Optional: Add a small delay to prevent overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`Sync completed. Total records inserted: ${totalInserted}`);
  } catch (error) {
    console.error('Error in KPI tracking sync job:', error);
  } finally {
    // Close PostgreSQL connection
    if (pgClient) {
      await pgClient.end();
      console.log('Closed PostgreSQL connection');
    }
  }
}

// Schedule the cron job to run daily at 1:00 AM
export function startKpiTrackingCronJob() {
  cron.schedule('0 1 * * *', async () => {
    console.log('Running KPI tracking sync job at', new Date().toISOString());
    await syncKpiTrackingData();
  });
  
  console.log('KPI tracking sync cron job scheduled');
}

// For manual testing
export async function runKpiTrackingSyncManually() {
  console.log('Manually running KPI tracking sync job');
  await syncKpiTrackingData();
}
