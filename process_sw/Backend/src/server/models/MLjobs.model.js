import mongoose from "mongoose";
const { Schema } = mongoose;

const MLJobSchema = new Schema({
  workflow_id: {
    type: Number,
    required: true,
  },
  file_id: {
    type: String,
    required: false,
  },
  filter_id: {
    type: Number,
    required: false,
  },
  user_id: {
    type: Number,
    required: false,
  },
  executed_by: {
    type: String,
    required: false,
  },
  file_name: {
    type: String,
    required: false,
  },
  settings: {
    type: Schema.Types.Mixed, // JSONB equivalent in Mongoose
    required: true
  },
  actual_settings: {
    type: Schema.Types.Mixed,
    required: true
  },
  ml_response: {
    type: Schema.Types.Mixed, // JSONB equivalent in Mongoose
    required: true
  },
  filter_id: {
    type: Number,
    required: false,
  },
    workflow_type: {
    type: String,
    required: false,
  },
  inputMaterials:{
    type: Schema.Types.Mixed,
    required: false
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'failed'],
    required: true
  }
}, { 
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
    collection: 'ml_jobs' // Use snake_case for the collection name
  },

); // Enable timestamps for createdAt and updatedAt

// Export the model
const MLJob = mongoose.model('MLJob', MLJobSchema);


export default MLJob;
