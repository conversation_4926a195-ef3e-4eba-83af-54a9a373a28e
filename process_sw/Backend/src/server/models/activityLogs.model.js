import mongoose from "mongoose";
const { Schema } = mongoose;

const ActivityLogSchema = new Schema({
  user_id: {
    type: Number,
    required: true,
  },
  last_used: {
    type: Date,
    default: null,
  },
  last_login: {
    type: Date,
    default: null,
  },

}, { 
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
    collection: 'activity_log' // Use snake_case for the collection name
  },

);

const ActivityLog = mongoose.model('ActivityLog', ActivityLogSchema);


export default ActivityLog;
