// Importing required modules
import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";

const CsvMapping = sequelize.define('csv_mapping', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    columns_mapping: {
        type: DataTypes.JSON,
        allowNull: true,
    },
    tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'Tenants',
            key: 'id'
        }
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'csv_mapping',
    timestamps: false
});

export default CsvMapping;