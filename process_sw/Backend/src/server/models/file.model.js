import { DataTypes, Model } from "sequelize";
import {sequelize} from "../database/dbConnection.js"; // Import the Sequelize instance correctly
import User from "./user.model.js";
import Tenants from "./tenants.model.js"; 
import Workflow from "./workflow.model.js";
class CSVFilesSchema extends Model {}

CSVFilesSchema.init(
  {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
      },
        csv_id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4, 
          allowNull: false,
          primaryKey: true, 
        },
        user_id: {
          type: DataTypes.INTEGER,
          allowNull: false,
          references: {
            model: 'Users', 
            key: 'id', 
          },
        },
        tenant_id: {
          type: DataTypes.INTEGER,
          allowNull: false,
          references: {
              model: Tenants, // Reference to Tenants model
              key: 'id',
          },
          onDelete: 'CASCADE', // Optional, deletes role if related tenant is deleted
      },
    //   workflow_id: {
    //     type: DataTypes.INTEGER,
    //     allowNull: false,
    //     references: {
    //         model: Workflow, 
    //     },
    //     onDelete: 'CASCADE', 
    // },
        version: {
          type: DataTypes.INTEGER,
          defaultValue: 1, 
          allowNull: false, 
        },
        file_name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      file_path: {
        type: DataTypes.STRING,
        allowNull: false
      },
      path_for_aiml: {
        type: DataTypes.STRING,
        allowNull: false
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true, 
      },
      aws_file_link: {
        type: DataTypes.STRING,
        allowNull: true, 
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false, 
        allowNull: false, 
      },
      hide_for_s3: {
        type: DataTypes.BOOLEAN,
        defaultValue: false, 
        allowNull: false, 
      },
      systems_id: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        defaultValue: [],
      },
      compatibility: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    parent_file_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
            model: 'CSVFiles',
            key: 'csv_id'
        }
    },
    type_of_file: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: false,
      defaultValue: ['processes']
    },
    clustering_data_response: {
      type: DataTypes.ENUM('in_progress', 'completed', 'failed'),
      allowNull: true,
      defaultValue: null
    },
  },
  {
    sequelize, 
    modelName: "CSVFiles",
    tableName: 'CSVFiles',
    timestamps: true,
    underscored: true,
  }
);

CSVFilesSchema.belongsTo(User, { 
  foreignKey: 'user_id',
  as: 'user'  // Alias name for the association
});

export default CSVFilesSchema;
