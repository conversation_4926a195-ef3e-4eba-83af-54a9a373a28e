import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
// const User = require('./user.model.js'); // Assuming you have a User model
import User from './user.model.js'
import Workflow from "./workflow.model.js";

const Folder = sequelize.define('Folder', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  hide_for_s3: {
    type: DataTypes.BOOLEAN,
    defaultValue: false, 
    allowNull: false, 
  },
  parent_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Folders',
      key: 'id',
    },
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  },
  systems_id: {
    type: DataTypes.ARRAY(DataTypes.STRING), // Changed to store system names (strings)
    allowNull: true,
    defaultValue: [], // Default to an empty array
  },
  type: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: 'workflow',
  },
}, {
  tableName: 'folders',
  timestamps: true, 
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

Folder.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Folder.hasMany(Folder, { foreignKey: 'parent_id', as: 'children' });
Folder.belongsTo(Folder, { foreignKey: 'parent_id', as: 'parent' });

Folder.associate = () => {
  Folder.hasMany(Workflow, { as: 'files', foreignKey: 'folder_id' });
  Workflow.belongsTo(Folder, { foreignKey: 'folder_id' });
};




export default Folder;
