// const { DataTypes } = require('sequelize');
import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";

const GlobalConfiguration = sequelize.define('global_configurations', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    configurations: {
        type: DataTypes.JSON,
        allowNull: false,
    },
    tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'Tenants',
            key: 'id'
        }
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'global_configurations',
    timestamps: false,
    indexes: [
        {
            fields: ['tenant_id', 'created_at'],
            name: 'idx_tenant_created'
        }
    ]
});

export default GlobalConfiguration;
