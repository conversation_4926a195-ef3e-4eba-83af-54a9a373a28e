import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import Systems from "./systems.model.js";
import Tenants from "./tenants.model.js";

const KpiTracking = sequelize.define('kpi_tracking', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    system_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: Systems,
            key: 'id'
        }
    },
    ph: {
        type: DataTypes.FLOAT,
        allowNull: false,
    },
    datetime: {
        type: DataTypes.DATE,
        allowNull: false,
    },
    recipe_no: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    vat_no: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    sequence_no: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    tenant_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Tenants,
        key: "id",
      },
      onDelete: "CASCADE",
    },
}, {
    tableName: 'kpi_tracking',
    timestamps: false,
});

// Set up association with Systems
KpiTracking.belongsTo(Systems, { foreignKey: 'system_id' });
KpiTracking.belongsTo(Tenants, { foreignKey: "tenant_id" });

export default KpiTracking; 