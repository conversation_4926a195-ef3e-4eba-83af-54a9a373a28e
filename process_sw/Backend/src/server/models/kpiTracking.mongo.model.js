import mongoose from "mongoose";
const { Schema } = mongoose;

const KpiTrackingSchema = new Schema(
  {
    id: { type: Number, required: true },
    tenantId: { type: Number, required: true },
    systemId: { type: Number, required: true },
    datetime: { type: Date, required: true },
  },
  {
    timestamps: { createdAt: "created_at", updatedAt: false },
    collection: "kpi_tracking",
    strict: false, // Allows new fields dynamically at the root level
  }
);

const KpiTrackingMongo = mongoose.model("KpiTracking", KpiTrackingSchema);
export default KpiTrackingMongo;
