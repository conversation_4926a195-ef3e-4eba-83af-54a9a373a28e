import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import Systems from "./systems.model.js";
import Tenants from "./tenants.model.js";

const KpiTrackingCarbon = sequelize.define('kpi_tracking_carbon', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    system_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: Systems,
            key: 'id'
        }
    },
    datetime: {
        type: DataTypes.DATE,
        allowNull: false,
    },
    material_id: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    recipe_id: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    furnace_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    rerun_iodine: {
        type: DataTypes.FLOAT,
        allowNull: false,
    },
    tenant_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Tenants,
        key: "id",
      },
      onDelete: "CASCADE",
    },
}, {
    tableName: 'kpi_tracking_carbon',
    timestamps: false,
});

// Set up associations
KpiTrackingCarbon.belongsTo(Systems, { foreignKey: 'system_id' });
KpiTrackingCarbon.belongsTo(Tenants, { foreignKey: "tenant_id" });

export default KpiTrackingCarbon; 