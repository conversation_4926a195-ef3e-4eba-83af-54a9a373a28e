import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";

const Operations = sequelize.define('operations', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
    },
    alias: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    icon: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'operations',
    timestamps: false,
});

// Operations.hasMany(OtherModel, { foreignKey: 'operation_id' });
// OtherModel.belongsTo(Operations, { foreignKey: 'operation_id' });

export default Operations;
