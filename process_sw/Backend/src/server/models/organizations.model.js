import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";

const Organizations = sequelize.define('organizations', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
    },
}, {
    tableName: 'organizations',
    timestamps: true,
});

export default Organizations;
