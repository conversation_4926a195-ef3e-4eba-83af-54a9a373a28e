import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import User from "./user.model.js";
import CSVFile from "./file.model.js";

const Pannel = sequelize.define("pannels", {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  configurations: {
    type: DataTypes.JSON,
    allowNull: false,
  },
  data_source_path: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'CSVFiles',
      key: 'id',
    },
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  },
  data_source: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'batch',
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: "pannels",
  timestamps: false,
});

// Associations
Pannel.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Pannel.belongsTo(CSVFile, { foreignKey: 'data_source_path', as: 'csvFile' });

export default Pannel;
