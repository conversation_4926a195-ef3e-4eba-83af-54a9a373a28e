import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";

const PannelType = sequelize.define("pannel_types", {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: "pannel_types",
  timestamps: false,
});

export default PannelType;
