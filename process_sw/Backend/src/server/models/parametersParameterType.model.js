import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance
import ParametersType from './parametersType.model.js';  // Import the Processes model
import Parameters from './parameters.model.js';  // Import the Parameters model

class ParametersParameterType extends Model {}

ParametersParameterType.init(
  {
    parameterstype_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: ParametersType, // Reference to the Processes model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a process deletes its associated parameters
    },
    parameter_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Parameters, // Reference to the Parameters model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a parameter deletes its associated processes
    },
  },
  {
    sequelize,
    modelName: 'ParametersParameterType',
    tableName: 'parameters_parameterstype',
    timestamps: false,  // This table may not need timestamps (createdAt, updatedAt)
    underscored: true, // Use snake_case for column names in the database
  }
);

// Setting up the associations (Optional, if you want to access related models)
ParametersType.belongsToMany(Parameters, {
  through: ParametersParameterType,
  foreignKey: 'parameterstype_id',
});

Parameters.belongsToMany(ParametersType, {
  through: ParametersParameterType,
  foreignKey: 'parameter_id',
});

export default ParametersParameterType;
