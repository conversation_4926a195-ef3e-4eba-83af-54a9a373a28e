import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance

class ParametersType extends Model {}

ParametersType.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false, // Ensuring the name is required
    },
  },
  {
    sequelize, 
    modelName: 'ParametersType',
    tableName: 'parameters_type',  // The table name in the database
    timestamps: false,  // Enable timestamps for createdAt and updatedAt
    underscored: true, // Use snake_case for column names in the database
  }
);

export default ParametersType;
