import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance
import Processes from './processes.model.js';  // Import the Processes model
import Parameters from './parameters.model.js';  // Import the Parameters model

class ProcessParameters extends Model {}

ProcessParameters.init(
  {
    process_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Processes, // Reference to the Processes model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a process deletes its associated parameters
    },
    parameter_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Parameters, // Reference to the Parameters model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a parameter deletes its associated processes
    },
  },
  {
    sequelize,
    modelName: 'ProcessParameters',
    tableName: 'process_parameters',
    timestamps: false,  // This table may not need timestamps (createdAt, updatedAt)
    underscored: true, // Use snake_case for column names in the database
  }
);

// Setting up the associations (Optional, if you want to access related models)
Processes.belongsToMany(Parameters, {
  through: ProcessParameters,
  foreignKey: 'process_id',
});

Parameters.belongsToMany(Processes, {
  through: ProcessParameters,
  foreignKey: 'parameter_id',
});

export default ProcessParameters;
