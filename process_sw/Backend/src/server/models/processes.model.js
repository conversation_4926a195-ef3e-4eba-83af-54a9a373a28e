import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance
import Systems from './systems.model.js';  // Import the Systems model for association

class Processes extends Model {}

Processes.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true, // Optionally, enforce uniqueness on the 'code' column
    },
    system_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: Systems,  // Reference to the Systems model
        key: 'id',  // The foreign key references 'id' in Systems model
      },
      onDelete: 'CASCADE',  // If a system is deleted, delete related processes as well
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    }
  },
  {
    sequelize, 
    modelName: 'Processes',
    tableName: 'processes',
    timestamps: true,  // Enable timestamps for createdAt and updatedAt
    underscored: true,  // Use snake_case for column names in the database
  }
);

// Establish the relationship with the Systems model (foreign key)
Processes.belongsTo(Systems, {
  foreignKey: 'system_id',
  as: 'system', // Alias for the association
});

export default Processes;
