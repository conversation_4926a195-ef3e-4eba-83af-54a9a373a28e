import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance

class Product extends Model {}

Product.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,  // Ensuring the name field is required
    },
    code: {
      type: DataTypes.STRING(500),
      allowNull: true,  // Optional field for product description
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    }
  },
  {
    sequelize, 
    modelName: 'Product',
    tableName: 'products',  // The table name in the database
    timestamps: false,  // Disable Sequelize's automatic timestamps
    underscored: true,  // Use snake_case for column names in the database
  }
);

export default Product;
