import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";

const qualityParameters = sequelize.define('quality_parameters', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    code: {
        type: DataTypes.STRING,
        allowNull: false,
    }
}, {
    tableName: 'quality_parameters',
    timestamps: false,
});

export default qualityParameters;