import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import Tenants from "./tenants.model.js"; // Assuming Tenants model is defined

const Roles = sequelize.define('roles', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: Tenants, // Reference to Tenants model
            key: 'id',
        },
        onDelete: 'CASCADE', // Optional, deletes role if related tenant is deleted
    },
    name: {
        type: DataTypes.ENUM('Process Engineer', 'Admin', 'Viewer', 'Editor'),
        allowNull: false,
    },
    alias: {
        type: DataTypes.STRING,
        allowNull: true, // Alias is optional
    },
    rank: {
        type: DataTypes.INTEGER,
        defaultValue: -1, // Default rank value for new roles
        allowNull: false,
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    modelName: "Roles",
    tableName: 'roles',
    timestamps: false,
});

export default Roles;
