import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance
import Tenants from "./tenants.model.js"; 
class Systems extends Model {}

Systems.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    tenant_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
          model: Tenants, // Reference to Tenants model
          key: 'id',
      },
      onDelete: 'CASCADE', // Optional, deletes role if related tenant is deleted
  },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
  },
  updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
  }
  },
  {
    sequelize,
    modelName: 'Systems',
    tableName: 'systems',  // Table name
    timestamps: true,  // Enable timestamps for createdAt and updatedAt
    underscored: true, // Use snake_case for column names in the database
  }
);

export default Systems;
