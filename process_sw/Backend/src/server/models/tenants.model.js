import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import Organizations from "./organizations.model.js"; // Assuming Organizations model is defined

const Tenants = sequelize.define('tenants', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    organization_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: Organizations, // Reference to Organizations model
            key: 'id',
        },
        onDelete: 'CASCADE', // Optional, deletes tenant if related organization is deleted
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    subdomain: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
    },
    aws_credentials: {
        type: DataTypes.JSON,
        allowNull: true,
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'tenants',
    timestamps: false,
});

export default Tenants;
