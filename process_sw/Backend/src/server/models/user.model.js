import { DataTypes, Model } from "sequelize";
import bcrypt from "bcrypt";
import {sequelize} from "../database/dbConnection.js"; // Import the Sequelize instance correctly
import Tenants from "./tenants.model.js";
import Roles from "./roles.model.js";
const saltRounds = 10;

class User extends Model {
  async isPasswordCorrect(password) {
    return await bcrypt.compare(password, this.password);
  }
}

User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    tenant_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Tenants,
        key: "id",
      },
      onDelete: "CASCADE",
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Roles,
        key: "id",
      },
      onDelete: "CASCADE",
    },
    first_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    last_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    password_hash: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    onboarding: {
      type: String,
      enum: ["in_progress", "completed"],
      default: "in_progress", // Sets the default value to 'in_progress'
    },
    selected_systems: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      allowNull: true,
      defaultValue: [],
    },
    two_factor_secret: {
      type: DataTypes.STRING,
      allowNull: true
    },
    two_factor_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    temp_2fa_secret: {
      type: DataTypes.STRING,
      allowNull: true
    },
    temp_2fa_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    email_verification_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    is_first_login: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
  },
  {
    sequelize, // Pass the Sequelize instance here
    modelName: "User",
    tableName: "users",
    timestamps: true,
    underscored: true,
    // hooks: {
    //   beforeSave: async (user) => {
    //     if (user.changed("password")) {
    //       user.password = await bcrypt.hash(user.password, saltRounds);
    //     }
    //   },
    // },
  }
);
User.belongsTo(Roles, {
  foreignKey: 'role_id',
  as: 'roles'
});

export default User;
