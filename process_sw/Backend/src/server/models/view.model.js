import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import Folder from "./folder.model.js";
import User from "./user.model.js";
import CSVFile from "./file.model.js" // You’ll need to ensure this exists
import ViewPannel from "./viewPannel.model.js";
const View = sequelize.define('views', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  },
  folder_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Folders',
      key: 'id',
    },
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  },
  structure: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {},
  },
  csvfile_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'CSVFiles',
      key: 'id',
    },
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'views',
  timestamps: false, // if you're manually managing timestamps
});

// Associations
View.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
View.belongsTo(Folder, { foreignKey: 'folder_id', as: 'folder' });
View.belongsTo(CSVFile, { foreignKey: 'csvfile_id', as: 'csvFile' });
View.hasMany(ViewPannel, {
  foreignKey: 'views_id',
  as: 'viewPanels' // Must match the `as` used in your include
});

export default View;
