import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import View from "./view.model.js"; // Assumes this is the model you shared earlier
import PannelType from "./pannelType.model.js"; // Ensure this exists

const ViewPannel = sequelize.define("view_pannel", {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  views_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'views',
      key: 'id',
    },
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  },
  configuration: {
    type: DataTypes.JSON,
    allowNull: false,
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
  pannel_type_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'pannel_types',
      key: 'id',
    },
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  },
}, {
  tableName: "view_pannel",
  timestamps: false,
});

// Associations


ViewPannel.associate = () => {
  const { View } = sequelize.models;
  
  // Workflow.hasMany(WorkflowStructure, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });
  // Workflow.hasMany(WorkflowComponents, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });
  
  // WorkflowStructure.belongsTo(Workflow, { foreignKey: 'workflow_id' });
  // WorkflowComponents.belongsTo(Workflow, { foreignKey: 'workflow_id' });

  ViewPannel.belongsTo(View, {
    foreignKey: "views_id",
    as: "view"
  });
};

ViewPannel.belongsTo(PannelType, {
  foreignKey: "pannel_type_id",
  as: "pannelType"
});

export default ViewPannel;
