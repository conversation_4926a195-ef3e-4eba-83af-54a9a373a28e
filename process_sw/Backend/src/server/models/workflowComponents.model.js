import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import Workflow from "./workflow.model.js";
import WorkflowStructure from "./workflowStructure.model.js";

const WorkflowComponents = sequelize.define('workflowcomponents', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    workflow_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'workflows',
            key: 'id'
        },
        onDelete: 'CASCADE'
    },
    component: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    settings: {
        type: DataTypes.JSON,
        allowNull: false,
    },
    type: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'workflowcomponents',
    timestamps: false,
});

// Workflow.hasMany(WorkflowStructure, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });
// Workflow.hasMany(WorkflowComponents, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });

// WorkflowStructure.belongsTo(Workflow, { foreignKey: 'workflow_id' });
// WorkflowComponents.belongsTo(Workflow, { foreignKey: 'workflow_id' });

WorkflowComponents.associate = () => {
    const { Workflow, WorkflowStructure } = sequelize.models;
    
    // Workflow.hasMany(WorkflowStructure, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });
    // Workflow.hasMany(WorkflowComponents, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });
    
    // WorkflowStructure.belongsTo(Workflow, { foreignKey: 'workflow_id' });
    // WorkflowComponents.belongsTo(Workflow, { foreignKey: 'workflow_id' });

    WorkflowComponents.belongsTo(Workflow, {
        foreignKey: 'workflow_id',
        as: 'workflow',
    });
};

export default WorkflowComponents;

