// const { DataTypes } = require('sequelize');
import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";

const workflowFilters = sequelize.define('workflow_filter_data', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    filter_type: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'Users',
            key: 'id'
        }
    },
    workflow_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'workflows',
            key: 'id'
        },
        onDelete: 'CASCADE'
    },
    csv_id: {
        type: DataTypes.UUID,
        allowNull: true
    },
    filters: {
        type: DataTypes.JSONB,
        allowNull: false,
    },
    systems_id: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        defaultValue: [],
    },
    tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    is_default_setting: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    },
    version: {
        type: DataTypes.INTEGER,
        default:1
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    }
}, {
    tableName: 'workflow_filters',
    timestamps: false,
});

export default workflowFilters;
