import { Router } from "express";
import { addLog, getTenants, UsersActivity, UsersDailyTimeSpent } from "../controllers/activityLog.controller.js";
import { authenticateToken } from "../middlewares/jwt.js";
const router = Router()


router.route('/').post(authenticateToken, addLog)
router.route('/user-details').post(authenticateToken, UsersActivity)
router.route('/user-time').post(authenticateToken, UsersDailyTimeSpent)
router.route('/tenants').get(authenticateToken, getTenants)


export default router