
import { Router } from "express";
import { authenticateToken } from "../middlewares/jwt.js";
import {saveStatisticalData , getStatisticalData , getStatisticalDataInputMaterialWise} from "../controllers/bucketization.controller.js"
const router = Router()


router.route('/save-statistical-data').post(authenticateToken , saveStatisticalData)
router.route('/get-statistical-data').get(authenticateToken , getStatisticalData)
router.route('/material-wise-statistical-data').get(authenticateToken , getStatisticalDataInputMaterialWise)
export default router