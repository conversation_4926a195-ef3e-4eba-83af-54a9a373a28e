import express from 'express';
import {authenticateToken} from '../middlewares/jwt.js'
import { createFolder, getAllFolders ,getAllFoldersWithViews} from '../controllers/folder.controllers.js';
const router = express.Router();


router.post('/create',authenticateToken, createFolder);
router.get('/all-folders',authenticateToken, getAllFolders);
router.get('/all-view-folders',authenticateToken, getAllFoldersWithViews);
export default router;
