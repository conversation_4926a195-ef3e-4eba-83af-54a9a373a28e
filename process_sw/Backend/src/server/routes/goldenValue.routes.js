import express from 'express';
import {authenticateToken} from '../middlewares/jwt.js'
import { 
    createGoldenValue, 
    getGoldenValues,
    updateGoldenValueName,
    deleteGoldenValue, 
    createParameter,
    compareGoldenValues
} from '../controllers/goldenValue.controllers.js';

const router = express.Router();

router.post('/create-parameter',authenticateToken, createParameter);
router.get('/values/compare',authenticateToken, compareGoldenValues);
router.post('/',authenticateToken, createGoldenValue);
router.get('/:id',authenticateToken, getGoldenValues);
router.put('/:id', authenticateToken, updateGoldenValueName);
router.delete('/:id', authenticateToken, deleteGoldenValue);

export default router;
