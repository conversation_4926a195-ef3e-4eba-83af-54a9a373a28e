import express from 'express';
import { authenticateToken } from '../middlewares/jwt.js';
import {
    createPannel,
    getPannels,
    getPannelById,
    updatePannel,
    deletePannel,
    savePannelFromState,
    getPannelsByDataSource,
    getPannelsByViewId,
    saveIndividualPannel,
    removeIndividualPannel
} from '../controllers/pannel.controllers.js';

const router = express.Router();

// Pannel CRUD routes
router.post('/create', authenticateToken, createPannel);
router.get('/list', authenticateToken, getPannels);
router.get('/:id', authenticateToken, getPannelById);
router.put('/:id', authenticateToken, updatePannel);
router.delete('/:id', authenticateToken, deletePannel);

// Save pannel from current state
router.post('/save-from-state', authenticateToken, savePannelFromState);

// Get pannels by data source
router.get('/data-source/:csvFileId', authenticateToken, getPannelsByDataSource);

// Get pannels by viewId (for compatibility with view-based frontend)
router.get('/view/:viewId', authenticateToken, getPannelsByViewId);

// Individual panel operations (replaces view panel operations)
router.post('/panel/save', authenticateToken, saveIndividualPannel);
router.delete('/panel/remove/:panelId', authenticateToken, removeIndividualPannel);

export default router;
