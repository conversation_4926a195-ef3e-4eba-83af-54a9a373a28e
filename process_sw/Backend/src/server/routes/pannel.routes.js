import express from 'express';
import { authenticateToken } from '../middlewares/jwt.js';
import { 
    createPannel,
    getPannels,
    getPannelById,
    updatePannel,
    deletePannel,
    savePannelFromState,
    getPannelsByDataSource
} from '../controllers/pannel.controllers.js';

const router = express.Router();

// Pannel CRUD routes
router.post('/create', authenticateToken, createPannel);
router.get('/list', authenticateToken, getPannels);
router.get('/:id', authenticateToken, getPannelById);
router.put('/:id', authenticateToken, updatePannel);
router.delete('/:id', authenticateToken, deletePannel);

// Save pannel from current state
router.post('/save-from-state', authenticateToken, savePannelFromState);

// Get pannels by data source
router.get('/data-source/:csvFileId', authenticateToken, getPannelsByDataSource);

export default router;
