import express from 'express';
import { createWorkflow, createWorkflowComponent, createWorkflowStructure, executeFlow, executeWorkflow, executeWorkflowData, getOperations, getWorkflow, getWorkflowById, getWorkflowStructures, insertFullWorkflowData, updateFullWorkflowData, updateWorkflow, updateWorkflowStructure ,deleteWorkflow,updateColumnsOrder, getConnectedTenants, getWorkflowRuns, getInProgressWorkflowRuns} from '../controllers/workflow.controllers.js'; // Adjust path as needed
import {authenticateToken} from '../middlewares/jwt.js'
import { createSharedWorkflow, getSharedWorkflow } from '../controllers/workflowShared.controller.js';
const router = express.Router();

// router.post('/create-workflows',authenticateToken , createWorkflow);
router.get('/', authenticateToken, getSharedWorkflow);
router.post('/', authenticateToken, createSharedWorkflow) ;

export default router;
