import { S3Client, PutObjectCommand, DeleteObjectCommand, ListObjectsV2Command, GetObjectCommand } from "@aws-sdk/client-s3";
import fs from "fs";
import 'dotenv/config';

// ✅ Configure AWS SDK v3 S3 Client
const s3 = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

// ✅ S3 Bucket Name
const BUCKET_NAME = process.env.AWS_BUCKET_NAME;

/**
 * ✅ Upload file to S3 (AWS SDK v3)
 * @param {string} filePath - Local file path
 * @param {string} key - S3 file key (path in bucket)
 */
const uploadFileToS3 = async (filePath, key, awsCredentials) => {
  try {
    const fileContent = fs.readFileSync(filePath);

    const s3Storage = new S3Client({
      region: awsCredentials?.AWS_REGION,
      // credentials: {
      //   accessKeyId: awsCredentials?.AWS_ACCESS_KEY_ID,
      //   secretAccessKey: awsCredentials?.AWS_SECRET_ACCESS_KEY,
      // },
    });

    let bucketName = awsCredentials?.AWS_BUCKET_NAME;

    const params = {
      Bucket: bucketName,
      Key: key,
      Body: fileContent,
    };

    await s3Storage.send(new PutObjectCommand(params));

    console.log("✅ File uploaded successfully:", `https://${bucketName}.s3.${awsCredentials?.AWS_REGION}.amazonaws.com/${key}`);
    return `https://${bucketName}.s3.${awsCredentials?.AWS_REGION}.amazonaws.com/${key}`;
  } catch (error) {
    console.error("❌ Upload Error:", error);
    throw error;
  }
};

/**
 * ✅ Delete file from S3 (AWS SDK v3)
 * @param {string} key - File key in S3 bucket
 */
const deleteFile = async (key) => {
  try {
    const params = {
      Bucket: BUCKET_NAME,
      Key: key,
    };

    await s3.send(new DeleteObjectCommand(params));
    console.log(`✅ File deleted successfully: ${key}`);
  } catch (error) {
    console.error("❌ Delete Error:", error);
    throw error;
  }
};

/**
 * ✅ Fetch all files in the S3 bucket (AWS SDK v3)
 */
const listFiles = async () => {
  try {
    const params = {
      Bucket: BUCKET_NAME,
    };

    const data = await s3.send(new ListObjectsV2Command(params));

    if (!data.Contents) {
      console.log("✅ No files found in S3.");
      return [];
    }

    const files = data.Contents.map((file) => file.Key);
    console.log('files', files)
    console.log("✅ Files in S3:", files);
    return files;
  } catch (error) {
    console.error("❌ List Error:", error);
    throw error;
  }
};

const getFileStream = async (key) => {
  try {
    const params = {
      Bucket: BUCKET_NAME,
      Key: key,
    };

    const response = await s3.send(new GetObjectCommand(params));

    return response.Body; // This is a readable stream
  } catch (error) {
    console.error("❌ Error fetching file from S3:", error);
    throw error;
  }
};

export {
  uploadFileToS3,
  deleteFile,
  listFiles,
  getFileStream
};
