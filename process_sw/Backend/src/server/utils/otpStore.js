const otpMap = new Map(); // key = `${email}-${purpose}`

/**
 * Store OTP for email and purpose (default is 'login').
 * @param {string} email 
 * @param {string} otp 
 * @param {'login' | 'forgot-password'} [purpose='login']
 */
function storeOtp(email, otp, purpose = 'login') {
  const key = `${email}-${purpose}`;
  otpMap.set(key, {
    otp,
    expiresAt: Date.now() + 5 * 60 * 1000,
  });
}

/**
 * Verify OTP for email and purpose (default is 'login').
 * @param {string} email 
 * @param {string | string[]} otp 
 * @param {'login' | 'forgot-password'} [purpose='login']
 * @returns {boolean}
 */
function verifyOtp(email, otp, purpose = 'login') {
  const key = `${email}-${purpose}`;
  const record = otpMap.get(key);
  if (!record) return false;

  const otpString = Array.isArray(otp) ? otp.join('') : otp;
  if (record.otp !== otpString) return false;
  if (Date.now() > record.expiresAt) return false;

  otpMap.delete(key);
  return true;
}

export default {
  storeOtp,
  verifyOtp,
};
