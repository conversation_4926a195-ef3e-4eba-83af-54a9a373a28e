# Filter Debugging Guide

This guide will help you debug the applied filters issue step by step.

## Steps to Debug

### 1. Test Filter Saving

1. **Open the application and navigate to a view with panels**
2. **Apply some filters** (e.g., select columns, set date range, add conditional filters)
3. **Open browser console** (F12 → Console tab)
4. **Save the panel** using the panel options menu
5. **Check console logs** for:
   ```
   Saving panel with data: {panelId: "...", appliedFilters: {...}}
   Applied filters being saved: {selectedColumns: {...}, dateFilter: {...}}
   ```

### 2. Check Backend Logs

1. **Check your backend console/logs** for:
   ```
   Received panel save request: {panelId: "...", appliedFilters: {...}}
   Saving panel with configurations: {appliedFilters: {...}}
   ```

### 3. Test Filter Loading

1. **Refresh the page** or navigate away and back
2. **Check browser console** for:
   ```
   Loading saved filters: {selectedColumns: {...}, dateFilter: {...}}
   Loading selected columns: {indices: [...], headers: [...]}
   Loading date filter: {startDate: "...", endDate: "..."}
   Loading conditional filters: [...]
   ```

### 4. Check Database

1. **Query your pannels table** to verify the data is saved:
   ```sql
   SELECT id, name, configurations FROM pannels WHERE user_id = YOUR_USER_ID;
   ```
2. **Check the configurations JSON** contains `appliedFilters`:
   ```json
   {
     "panelType": "TimeSeriesPanel",
     "appliedFilters": {
       "selectedColumns": {...},
       "dateFilter": {...},
       "conditionalFilters": [...]
     }
   }
   ```

## Common Issues and Solutions

### Issue 1: Filters Not Being Saved

**Symptoms:** Console shows empty or default filters being saved
**Possible Causes:**
- `appliedFilters` prop not being passed correctly to PanelOptionsMenu
- Filter state not being captured properly in GridItem

**Solution:**
1. Check if `getAppliedFilters()` in GridItem returns the correct data
2. Verify that filter state variables have the expected values
3. Add console.log in GridItem to see what filters are being passed

### Issue 2: Filters Not Being Loaded

**Symptoms:** Console shows filters being loaded but UI doesn't reflect them
**Possible Causes:**
- Filter state not being applied to components
- Components not reacting to filter state changes
- Timing issues with state updates

**Solution:**
1. Check if state variables are being updated correctly
2. Verify that components are receiving the updated props
3. Check for useEffect dependencies in panel components

### Issue 3: Backend Not Receiving Filters

**Symptoms:** Backend logs show undefined or empty appliedFilters
**Possible Causes:**
- Frontend not sending the data correctly
- Request body structure mismatch

**Solution:**
1. Check network tab in browser dev tools
2. Verify the request payload contains appliedFilters
3. Check if the backend route is correctly extracting the data

### Issue 4: Database Not Storing Filters

**Symptoms:** Backend receives filters but database doesn't contain them
**Possible Causes:**
- JSON field not being updated properly
- Database constraints or validation issues

**Solution:**
1. Check database logs for errors
2. Verify the JSON structure is valid
3. Check if the update/create operation is successful

## Debug Console Commands

Add these to your browser console to check current state:

```javascript
// Check current filter state
console.log('Selected Columns:', window.selectedColumns);
console.log('Date Filter:', window.dateFilter);
console.log('Conditional Filters:', window.conditionalFilters);

// Check if filters are being passed to panels
const gridItems = document.querySelectorAll('[data-grid]');
console.log('Number of grid items:', gridItems.length);
```

## Testing Checklist

- [ ] Filters are applied in UI
- [ ] Console shows correct filter data when saving
- [ ] Backend receives filter data
- [ ] Database contains saved filter data
- [ ] Page refresh triggers filter loading
- [ ] Console shows filters being loaded
- [ ] UI reflects loaded filters
- [ ] All filter types work (columns, date, conditional)

## Expected Console Output

### When Saving:
```
Saving panel with data: {
  panelId: "item-1",
  panelType: "TimeSeriesPanel",
  appliedFilters: {
    selectedColumns: {indices: [1, 2], headers: ["temp", "pressure"]},
    dateFilter: {startDate: "2024-01-01", endDate: "2024-12-31"},
    conditionalFilters: [{id: "filter-1", column: "status", operator: "equals", value: "active"}]
  }
}
```

### When Loading:
```
Loading saved filters: {
  selectedColumns: {indices: [1, 2], headers: ["temp", "pressure"]},
  dateFilter: {startDate: "2024-01-01", endDate: "2024-12-31"},
  conditionalFilters: [{id: "filter-1", column: "status", operator: "equals", value: "active"}]
}
Loading selected columns: {indices: [1, 2], headers: ["temp", "pressure"]}
Loading date filter: {startDate: "2024-01-01", endDate: "2024-12-31"}
Loading conditional filters: [{id: "filter-1", column: "status", operator: "equals", value: "active"}]
```

## Next Steps

1. **Follow the debugging steps above**
2. **Check console logs** at each step
3. **Identify where the flow breaks**
4. **Report findings** with specific console output and error messages

If you find the issue, let me know what step failed and what the console output shows, and I can provide a targeted fix.
