import './App.css';
import 'reactflow/dist/style.css';
import './styles/flow.css';
import Routes from './routes';
import { AuthProvider } from './context/AuthContext';
import { Provider } from 'react-redux';
import store from './Redux/store';
import Chatbot from './components/Chat/ChatBot';
import Notiflix from 'notiflix';

function App() {

  // Initialize Notiflix Notify
  Notiflix.Notify.init({
    closeButton: true,
    clickToClose: true,
  });

  // Initialize Notiflix Loading with the project's primary blue color
  Notiflix.Loading.init({
    svgColor: '#252963',
    svgSize: '60px',
    messageFontSize: '14px',
    messageColor: '#252963',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  });

  if (process.env.NODE_ENV !== 'development') console.log = () => {}

  return (
    <Provider store={store}>
      <AuthProvider>
        <div className="App">
          <Routes />
          {/* <Chatbot /> */}
        </div>
      </AuthProvider>
    </Provider>

  );
}

export default App;
