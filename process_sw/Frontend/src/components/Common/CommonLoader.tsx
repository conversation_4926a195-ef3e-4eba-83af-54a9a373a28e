import ReactDOM from 'react-dom/client';
import React from 'react';
import { Spin } from 'antd';

let loaderRoot: ReactDOM.Root | null = null;

export const showFullscreenLoader = () => {
  const existingDiv = document.getElementById('fullscreen-loader');

  if (existingDiv) {
    return;
  }

  const containerDiv = document.createElement('div');
  containerDiv.id = 'fullscreen-loader';

  Object.assign(containerDiv.style, {
    position: 'fixed',
    top: '0',
    left: '0',
    width: '100vw',
    height: '100vh',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff16',
    zIndex: '9999',
  });

  document.body.appendChild(containerDiv);

  loaderRoot = ReactDOM.createRoot(containerDiv);
  loaderRoot.render(<Spin size="large" />);
};

export const hideFullscreenLoader = () => {
  const containerDiv = document.getElementById('fullscreen-loader');

  if (!loaderRoot || !containerDiv) {
    return;
  }

  try {
    loaderRoot.unmount();
    document.body.removeChild(containerDiv);
    loaderRoot = null;
  } catch (error) {
    console.error('Error hiding fullscreen loader:', error);
  }
};
