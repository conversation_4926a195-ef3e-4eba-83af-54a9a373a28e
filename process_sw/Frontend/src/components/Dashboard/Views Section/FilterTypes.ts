// Define filter types for panels
import { ColumnSelection, DateFilter } from './types';

// Base filter interface that all panel filters will extend
export interface BasePanelFilter {
  id: string;
  type: string;
  enabled: boolean;
}

// Column filter for selecting specific columns
export interface ColumnFilter extends BasePanelFilter {
  type: 'column';
  selection: ColumnSelection;
}

// Row filter removed as it's not used

// Date range filter
export interface DateRangeFilter extends BasePanelFilter {
  type: 'date';
  startDate: string | null;
  endDate: string | null;
}

// Value range filter for numeric columns
export interface ValueRangeFilter extends BasePanelFilter {
  type: 'value-range';
  column: string;
  min: number;
  max: number;
}

// Conditional column filter for applying conditions on columns
export interface ConditionalColumnFilter extends BasePanelFilter {
  type: 'conditional-column';
  column: string;
  operator: '=' | '!=' | '>' | '>=' | '<' | '<=' | 'contains' | 'starts-with' | 'ends-with' | 'by_value' | 'between' | 'not_between';
  value: string | number | string[] | number[];
}

// Union type of all filter types
export type PanelFilter = ColumnFilter | DateRangeFilter | ValueRangeFilter | ConditionalColumnFilter;

// Filter state for a panel
export interface PanelFilterState {
  panelId: string;
  filters: PanelFilter[];
}

// Helper functions for working with filters

// Create a column filter
export function createColumnFilter(id: string, selection: ColumnSelection, enabled = true): ColumnFilter {
  return {
    id,
    type: 'column',
    enabled,
    selection
  };
}

// Row filter function removed as it's not used

// Create a date filter
export function createDateFilter(id: string, startDate: string | null, endDate: string | null, enabled = true): DateRangeFilter {
  return {
    id,
    type: 'date',
    enabled,
    startDate,
    endDate
  };
}

// Create a value range filter
export function createValueRangeFilter(id: string, column: string, min: number, max: number, enabled = true): ValueRangeFilter {
  return {
    id,
    type: 'value-range',
    enabled,
    column,
    min,
    max
  };
}

// Create a conditional column filter
export function createConditionalColumnFilter(
  id: string,
  column: string,
  operator: ConditionalColumnFilter['operator'],
  value: string | number,
  enabled = true
): ConditionalColumnFilter {
  return {
    id,
    type: 'conditional-column',
    enabled,
    column,
    operator,
    value
  };
}

// Get a filter by type from a panel's filter state
export function getFilterByType<T extends PanelFilter>(filters: PanelFilter[], type: string): T | undefined {
  return filters.find(filter => filter.type === type) as T | undefined;
}

// Update a filter in a panel's filter state
export function updateFilter(filters: PanelFilter[], updatedFilter: any): PanelFilter[] {
  const index = filters.findIndex(filter => filter.id === updatedFilter.id);
  if (index === -1) {
    return [...filters, updatedFilter];
  }

  const newFilters = [...filters];
  newFilters[index] = updatedFilter;
  return newFilters;
}

// Convert legacy filter props to the new filter system
export function convertLegacyFilters(
  selectedColumns: ColumnSelection,
  dateFilter: DateFilter
): PanelFilter[] {
  const filters: PanelFilter[] = [];

  // Add column filter if there are selected columns
  if (selectedColumns.indices.length > 0) {
    filters.push(createColumnFilter('column-filter', selectedColumns));
  }

  // Add date filter if there is a date range
  if (dateFilter.startDate || dateFilter.endDate) {
    filters.push(createDateFilter('date-filter', dateFilter.startDate, dateFilter.endDate));
  }

  return filters;
}
