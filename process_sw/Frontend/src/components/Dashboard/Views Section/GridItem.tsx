import React, { useState, useRef } from 'react';
import { ComponentType, GridItemData, ColumnSelection, DateFilter } from './types';
import { PanelFilter } from './FilterTypes';
import TimeSeriesPanel from './panels/TimeSeriesPanel';
import OverviewPanel from './panels/OverviewPanel';
import HistogramPanel from './panels/HistogramPanel';
import DataTablePanel from './panels/DataTablePanel';
import { CloseOutlined, ExpandOutlined } from '@ant-design/icons';
import PanelOptionsMenu from './PanelOptionsMenu';
import FullScreenModal from './FullScreenModal';

interface GridItemProps {
  file_id: string;
  data: GridItemData;
  fileData: any;
  filteredData?: any; // Pre-filtered data
  onRemove: (id: string) => void;

  // Selection and filter props
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;

  // Panel-specific filters
  panelFilters?: PanelFilter[];
  conditionalFilters?: PanelFilter[];

  // Selection and filter handlers
  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string , data?:any) => void;

  // Filter management handlers
  onAddFilter?: (filter: PanelFilter) => void;
  onRemoveFilter?: (filterId: string) => void;

  // Layout information for saving
  layout?: { x: number, y: number, w: number, h: number };
}

const GridItem: React.FC<GridItemProps> = ({
  data,
  fileData,
  filteredData,
  file_id,
  onRemove,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = [],
  conditionalFilters = [],
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
  onAddFilter,
  onRemoveFilter,
  layout
}) => {
  // State for panel display
  const [isFullScreen, setIsFullScreen] = useState(false);
  const panelContentRef = useRef<HTMLDivElement>(null);

  const effectiveColumnSelection = selectedColumns;

  const renderComponent = () => {
    switch (data.type) {
      case ComponentType.TimeSeriesPanel:
        return (
          <TimeSeriesPanel
            data={fileData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
          />
        );
      case ComponentType.OverviewPanel:
        return (
          <OverviewPanel
            data={fileData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
          />
        );
      case ComponentType.HistogramPanel:
        return (
          <HistogramPanel
            data={fileData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
          />
        );
      case ComponentType.DataTablePanel:
        return (
          <DataTablePanel
            data={fileData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            conditionalFilters={conditionalFilters}
            onAddFilter={onAddFilter}
            onRemoveFilter={onRemoveFilter}
          />
        );
      default:
        return <div>Unknown panel type</div>;
    }
  };

  // Get panel configuration for saving
  const getPanelConfiguration = () => {
    return {
      selectedColumns: effectiveColumnSelection,
      dateFilter: dateFilter,
      panelFilters: panelFilters,
      conditionalFilters: conditionalFilters,
      fileId: file_id
    };
  };

  // Get applied filters for saving
  const getAppliedFilters = () => {
    const filters = {
      selectedColumns: effectiveColumnSelection,
      dateFilter: dateFilter,
      panelFilters: panelFilters,
      conditionalFilters: conditionalFilters,
      valueRangeFilters: [] // Add if you have value range filters
    };

    console.log('GridItem - Getting applied filters for panel:', data.id, filters);
    return filters;
  };

  const handleRemove = () => {
    onRemove(data.id);
  };

  return (
    <>
      <div className="grid-item">
        <div className="grid-item-header">
          <div className="grid-item-title drag-handle">{data.title}</div>
          <div className="grid-item-controls no-drag">
            <button
              className="grid-item-control"
              title="Expand panel"
              onClick={() => setIsFullScreen(true)}
            >
              <ExpandOutlined />
            </button>
            <PanelOptionsMenu
              panelType={data.type}
              panelRef={panelContentRef}
              panelTitle={data.title}
              configuration={getPanelConfiguration()}
              fileId={file_id}
              panelId={data.id}
              layout={layout}
              onRemove={onRemove}
              appliedFilters={getAppliedFilters()}
            />
            <button
              className="grid-item-control"
              title="Remove from view (temporary)"
              onClick={handleRemove}
            >
              <CloseOutlined />
            </button>
          </div>
        </div>
        <div className="grid-item-content no-drag" style={{overflow:'hidden'}} ref={panelContentRef}>{renderComponent()}</div>
      </div>

      {/* Full Screen Modal */}
      <FullScreenModal
        isOpen={isFullScreen}
        onClose={() => setIsFullScreen(false)}
        title={data.title}
      >
        <div className="full-screen-panel-content" style={{ width: '100%', height: '100%' }}>
          {/* Create a new instance of the component for full screen view */}
          {renderComponent()}
        </div>
      </FullScreenModal>
    </>
  );
};

export default GridItem;
