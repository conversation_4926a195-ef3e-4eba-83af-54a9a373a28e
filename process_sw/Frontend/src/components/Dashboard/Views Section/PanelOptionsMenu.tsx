import React, { useState } from 'react';
import { Dropdown, Button, Modal, Form, Input, message, Popconfirm } from 'antd';
import {
  EllipsisOutlined,
  DownloadOutlined,
  SaveOutlined,
  DeleteOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import { postRequest, deleteRequest } from '../../../utils/apiHandler';
import Notiflix from 'notiflix';

// Dynamic import for html2canvas
const importHtml2Canvas = async () => {
  try {
    const html2canvas = await import('html2canvas');
    return html2canvas.default;
  } catch (error) {
    console.error('Failed to load html2canvas:', error);
    message.error('Failed to load image export library');
    return null;
  }
};

interface PanelOptionsMenuProps {
  panelType: string;
  panelRef: React.RefObject<HTMLDivElement>;
  panelTitle: string;
  configuration: any;
  fileId?: string | number;
  panelId?: string;
  layout?: { x: number, y: number, w: number, h: number };
  onRemove?: (id: string) => void;
  appliedFilters?: {
    selectedColumns?: { indices: number[], headers: string[] };
    dateFilter?: { startDate: string | null, endDate: string | null };
    panelFilters?: any[];
    conditionalFilters?: any[];
    valueRangeFilters?: any[];
  };
}

const PanelOptionsMenu: React.FC<PanelOptionsMenuProps> = ({
  panelType,
  panelRef,
  panelTitle,
  configuration,
  fileId,
  panelId,
  layout,
  onRemove,
  appliedFilters
}) => {
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [savePannelModalVisible, setSavePannelModalVisible] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [isSavingPannel, setIsSavingPannel] = useState(false);
  const [form] = Form.useForm();
  const [pannelForm] = Form.useForm();

  // Export panel as image
  const exportAsImage = async () => {
    if (!panelRef.current) return;

    try {
      message.loading('Generating image...', 0);

      // Dynamically import html2canvas
      const html2canvasModule = await importHtml2Canvas();
      if (!html2canvasModule) {
        message.destroy();
        return;
      }

      const canvas = await html2canvasModule(panelRef.current, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true,
        allowTaint: true
      });

      // Create download link
      const link = document.createElement('a');
      link.download = `${panelTitle.replace(/\s+/g, '_')}_panel.png`;
      link.href = canvas.toDataURL();
      link.click();

      message.destroy();
      message.success('Panel exported as image successfully');
    } catch (error) {
      message.destroy();
      console.error('Error exporting panel as image:', error);
      message.error('Failed to export panel as image');
    }
  };

  // Export panel as PDF
  const exportAsPDF = async () => {
    if (!panelRef.current) return;

    try {
      message.loading('Generating PDF...', 0);

      // Dynamically import libraries
      const [html2canvasModule, jsPDFModule] = await Promise.all([
        importHtml2Canvas(),
        import('jspdf').then(module => module.jsPDF)
      ]);

      if (!html2canvasModule || !jsPDFModule) {
        message.destroy();
        return;
      }

      const canvas = await html2canvasModule(panelRef.current, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true,
        allowTaint: true
      });

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDFModule();

      // Calculate dimensions to fit the page
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      // Add image to PDF
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add new pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`${panelTitle.replace(/\s+/g, '_')}_panel.pdf`);

      message.destroy();
      message.success('Panel exported as PDF successfully');
    } catch (error) {
      message.destroy();
      console.error('Error exporting panel as PDF:', error);
      message.error('Failed to export panel as PDF');
    }
  };

  // Save individual panel
  const saveIndividualPanel = async () => {
    if (!fileId || !panelId) {
      message.error('Panel information missing');
      return;
    }

    setIsSaving(true);

    try {
      Notiflix.Loading.circle('Saving panel...');

      const panelData = {
        panelId: panelId,
        panelType: panelType,
        title: panelTitle,
        csvFileId: fileId,
        configuration: {
          ...configuration,
          title: panelTitle
        },
        position: layout || { x: 0, y: 0, w: 6, h: 6 },
        appliedFilters: appliedFilters || {
          selectedColumns: { indices: [], headers: [] },
          dateFilter: { startDate: null, endDate: null },
          panelFilters: [],
          conditionalFilters: [],
          valueRangeFilters: []
        }
      };

      const response = await postRequest('/pannel/panel/save', panelData);

      if (response.data && response.data.status === 200) {
        message.success(`${panelTitle} saved successfully`);
      } else {
        message.error('Failed to save panel');
      }
    } catch (error) {
      console.error('Error saving panel:', error);
      message.error('Error saving panel');
    } finally {
      Notiflix.Loading.remove();
      setIsSaving(false);
    }
  };

  // Remove individual panel
  const removeIndividualPanel = async () => {
    if (!panelId) {
      message.error('Panel ID missing');
      return;
    }

    setIsRemoving(true);

    try {
      Notiflix.Loading.circle('Removing panel...');

      const response = await deleteRequest(`/pannel/panel/remove/${panelId}`);

      if (response.data && response.data.status === 200) {
        message.success(`${panelTitle} removed successfully`);
        // Call the onRemove callback to update the UI
        if (onRemove) {
          onRemove(panelId);
        }
      } else {
        message.error('Failed to remove panel');
      }
    } catch (error) {
      console.error('Error removing panel:', error);
      message.error('Error removing panel');
    } finally {
      Notiflix.Loading.remove();
      setIsRemoving(false);
    }
  };

  // Get panel type ID for saving
  const getPanelTypeId = (type: string): number => {
    switch (type) {
      case 'TimeSeriesPanel':
        return 1;
      case 'OverviewPanel':
        return 2;
      case 'HistogramPanel':
        return 3;
      case 'DataTablePanel':
        return 4;
      default:
        return 1;
    }
  };

  // Save panel as a pannel
  const savePanelAsPannel = async (values: { pannelName: string }) => {
    if (!fileId || !panelId) {
      message.error('Panel information missing');
      return;
    }

    setIsSavingPannel(true);

    try {
      Notiflix.Loading.circle('Saving pannel...');

      const pannelData = {
        name: values.pannelName,
        panelType: panelType,
        title: panelTitle,
        csvFileId: fileId,
        configuration: {
          ...configuration,
          title: panelTitle
        },
        position: layout || { x: 0, y: 0, w: 6, h: 6 },
        panelId: panelId,
        appliedFilters: appliedFilters || {
          selectedColumns: { indices: [], headers: [] },
          dateFilter: { startDate: null, endDate: null },
          panelFilters: [],
          conditionalFilters: [],
          valueRangeFilters: []
        }
      };

      const response = await postRequest('/pannel/save-from-state', pannelData);

      if (response.data && response.data.status === 201) {
        message.success(`Pannel "${values.pannelName}" saved successfully`);
        setSavePannelModalVisible(false);
        pannelForm.resetFields();
      } else {
        message.error('Failed to save pannel');
      }
    } catch (error) {
      console.error('Error saving pannel:', error);
      message.error('Error saving pannel');
    } finally {
      Notiflix.Loading.remove();
      setIsSavingPannel(false);
    }
  };

  // Save panel as a separate view
  const saveAsView = async (values: { viewName: string }) => {
    try {
      Notiflix.Loading.standard('Saving view...');

      // Get the current file ID from the global variable or from props
      const csvFileId = fileId

      if (!csvFileId) {
        message.error('No file selected. Please select a file first.');
        Notiflix.Loading.remove();
        return;
      }

      // Create a new view with just this panel
      const viewData = {
        csvfile_id: csvFileId,
        name: values.viewName,
        description: `Created from ${panelTitle} panel`,
        panels: [
          {
            panel_type_id: getPanelTypeId(panelType),
            configuration: {
              title: panelTitle,
              position: {
                x: 0,
                y: 0,
                w: 12,
                h: 8
              },
              ...configuration
            }
          }
        ],
        structure: [
          {
            i: 'item-1',
            x: 0,
            y: 0,
            w: 12,
            h: 8,
            panelType: panelType
          }
        ]
      };

      const response = await postRequest('/view/create-view', viewData);

      if (response.data) {
        message.success(`View "${values.viewName}" created successfully!`);
        setSaveModalVisible(false);
        form.resetFields();
      } else {
        message.error('Failed to create view');
      }

      Notiflix.Loading.remove();
    } catch (error) {
      console.error('Error creating view:', error);
      message.error('Error creating view');
      Notiflix.Loading.remove();
    }
  };

  // Create a wrapper component for the remove panel with Popconfirm
  const RemovePanelItem = () => (
    <Popconfirm
      title="Remove Panel"
      description="Are you sure you want to remove this panel? This will delete it from the database."
      onConfirm={removeIndividualPanel}
      okText="Yes"
      cancelText="No"
      disabled={isRemoving}
      okButtonProps={{ danger: true }}
    >
      <div style={{ padding: '5px 12px', cursor: isRemoving ? 'not-allowed' : 'pointer', opacity: isRemoving ? 0.6 : 1 }}>
        <DeleteOutlined style={{ marginRight: 8 }} />
        {isRemoving ? 'Removing...' : 'Remove Panel'}
      </div>
    </Popconfirm>
  );

  // Define menu items for the dropdown
  const menuItems = [
    {
      key: 'save-panel',
      label: isSaving ? 'Saving...' : 'Save Panel',
      icon: <SaveOutlined />,
      disabled: isSaving,
      onClick: saveIndividualPanel
    },
    {
      key: 'save-pannel',
      label: isSavingPannel ? 'Saving...' : 'Save as Pannel',
      icon: <SaveOutlined />,
      disabled: isSavingPannel,
      onClick: () => setSavePannelModalVisible(true)
    },
    {
      key: 'remove-panel',
      label: <RemovePanelItem />,
      disabled: isRemoving
    },
    {
      key: 'export',
      label: 'Export',
      icon: <DownloadOutlined />,
      children: [
        {
          key: 'image',
          label: 'Export as Image',
          onClick: exportAsImage
        },
        {
          key: 'pdf',
          label: 'Export as PDF',
          onClick: exportAsPDF
        }
      ]
    },
    // {
    //   key: 'save',
    //   label: 'Save as View',
    //   icon: <SaveOutlined />,
    //   onClick: () => setSaveModalVisible(true)
    // }
  ];

  return (
    <>
      <Dropdown menu={{ items: menuItems }} trigger={['click']} placement="bottomRight">
        <Button
          type="text"
          icon={<EllipsisOutlined />}
          className="panel-options-button"
          onClick={(e) => e.stopPropagation()}
        />
      </Dropdown>

      <Modal
        title="Save as Pannel"
        open={savePannelModalVisible}
        onCancel={() => setSavePannelModalVisible(false)}
        footer={null}
      >
        <Form
          form={pannelForm}
          layout="vertical"
          onFinish={savePanelAsPannel}
        >
          <Form.Item
            name="pannelName"
            label="Pannel Name"
            rules={[{ required: true, message: 'Please enter a name for the pannel' }]}
          >
            <Input placeholder="Enter pannel name" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={isSavingPannel}>
              Save Pannel
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="Save Panel as View"
        open={saveModalVisible}
        onCancel={() => setSaveModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveAsView}
        >
          <Form.Item
            name="viewName"
            label="View Name"
            rules={[{ required: true, message: 'Please enter a name for the view' }]}
          >
            <Input placeholder="Enter view name" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              Save
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default PanelOptionsMenu;
