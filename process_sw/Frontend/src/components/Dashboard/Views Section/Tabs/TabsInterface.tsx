import { useEffect } from "react";
// import { useTabs } from "../../../../hooks/useTabs";
import TabsContainer from "./TabsContainer";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from 'react-redux';
import {
    addTab,
    setActiveTab,
    closeTab,
    closeAllTabs,
    closeOtherTabs,
    closeTabsToRight,
    startTabDrag,
    dropTab,
    endTabDrag,
    showContextMenu,
    hideContextMenu,
    } from "../../../../Redux/slices/viewTabSlice"
import store from "../../../../Redux/store";
    
export default function TabsInterface() {
  const dispatch = useDispatch();
  const tabsdata = useSelector((state:any) => state?.viewTabs?.tabs);
  const activeTabId = useSelector((state: any) => state?.viewTabs?.activeTabId);
  const contextMenuState = useSelector((state: any) => state?.viewTabs?.contextMenuState);
  const navigate = useNavigate();
 


  const handleTabClick = (tabId: string) => {
    dispatch(setActiveTab(tabId));
    navigate('/?tab=insight&viewId=' + tabId);
  };

  const handleTabClose = async (tabId: string) => {
    await dispatch(closeTab(tabId));
    handleNavigation()
  };

  const handleTabDragStart = (tabId: string) => {
    dispatch(startTabDrag(tabId));
  };

  const handleTabDrop = (sourceId: string, targetId: string) => {
    dispatch(dropTab({ sourceId, targetId }));
  };

  const handleTabDragEnd = () => {
    dispatch(endTabDrag());
  };

  const handleContextMenu = (event: React.MouseEvent, tabId: string) => {
    event.preventDefault();
    dispatch(showContextMenu({
      // isVisible: true,
      x: event.clientX,
      y: event.clientY,
      tabId
    }));
  };

  const hideContextMenuData = () =>{
    dispatch(hideContextMenu());
  }
  
  const handleNavigation = () =>{
    const state = store.getState();
    const newActiveTabId = state.viewTabs.activeTabId;
    navigate('/?tab=insight&viewId=' + newActiveTabId);
  }

  const handleContextMenuAction = (action: string, tabId: string) => {
  switch (action) {
      case 'closeTab':
        dispatch(closeTab(tabId));
        dispatch(hideContextMenu())
        handleNavigation()
        break;

      case 'closeAllTabs':
        dispatch(closeAllTabs());
        dispatch(hideContextMenu())
        navigate('/?tab=insight&workflowId=0');
        break;

      case 'closeOtherTabs':
        dispatch(closeOtherTabs(tabId));
        dispatch(hideContextMenu())
        navigate('/?tab=insight&viewId=' + tabId);
        break;

      case 'closeTabsToRight':
        dispatch(closeTabsToRight(tabId));
        dispatch(hideContextMenu())
        handleNavigation()
        break;

      default:
        console.warn(`Unknown context menu action: ${action}`);
    }
  };

  const addNewTab = (fileType: 'ts' | 'js' | 'jsx' | 'tsx' = 'ts') => {
    dispatch(addTab({ fileType ,id:999 ,viewName:'new view'}));
  };


  return (
    <div>
    
      
      {/* Tabs bar */}
      <div className="flex items-center bg-[#F2F2F2] border-b border-solid border-[#DEDEDE]">
        <TabsContainer
          tabs={tabsdata}
          activeTabId={activeTabId}
          contextMenuState={contextMenuState}
          onTabClick={handleTabClick}
          onTabClose={handleTabClose}
          onTabDragStart={handleTabDragStart}
          onTabDrop={handleTabDrop}
          onTabDragOver={handleTabDragEnd}
          onTabDragEnd={handleTabDragEnd}
          onContextMenu={handleContextMenu}
          onContextMenuAction={handleContextMenuAction}
          onContextMenuClose={hideContextMenuData}
          onAddTab={addNewTab}
        />
      </div>
    </div>
  );
}
