export interface Tab {
    id: string;
    label: string;
    icon: string; // 'ts' | 'js' | etc.
    content?: string;
    active: boolean;
    modified: boolean;
  }
  
  export interface ContextMenuState {
    isVisible: boolean;
    x: number;
    y: number;
    tabId: string;
  }
  
  export type ContextMenuAction = 
    | "closeTab" 
    | "closeAllTabs" 
    | "closeOtherTabs" 
    | "closeTabsToRight"
  