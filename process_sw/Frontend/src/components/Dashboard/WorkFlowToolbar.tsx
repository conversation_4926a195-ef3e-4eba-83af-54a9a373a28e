import React from 'react';
import { <PERSON>lt<PERSON>, Typography, Space, Button, Divider } from 'antd';
import arrowup from '../../img/ArrowUUpLeft-r.svg';
import arrowright from '../../img/ArrowUUpRight-r.svg';
import chart1 from '../../img/chart1.svg';
import arrowdown from '../../img/arrowdown.svg';
import arrowdownlight from '../../img/arrowdownlight.svg';
import union from '../../img/Union.svg';
import chain from '../../img/chain.svg';
import capsule from '../../img/capsule.svg';
import compare from '../../img/compare.svg';
import lane from '../../img/lane.svg';
import yaxis from '../../img/yaxis.svg';
import reset from '../../img/reset.svg';
import dimming from '../../img/dimming.svg';
import certainty from '../../img/certainty.svg';
import label from '../../img/label.svg';
import gridlines from '../../img/gridlines.svg';
import summary from '../../img/summary.svg';
import tooltip from '../../img/tooltip.svg';
import color from '../../img/color.svg';
import zoomin from '../../img/zoomin.svg';
import zoomout from '../../img/zoomout.svg';
import feedback from '../../img/feedback.svg';
// import { DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Dropdown } from 'antd';

const items: MenuProps['items'] = [
  {
    label: <a href="#">1st menu item</a>,
    key: '0',
  },
  {
    label: <a href="#">2nd menu item</a>,
    key: '1',
  },
  {
    type: 'divider',
  },
  {
    label: '3rd menu item',
    key: '3',
  },
];

function Header() {
  return (
    <React.Fragment>
      <div className="insight-header">
        <Space>
          <Tooltip title="Arrow Up">
            <Button type="text" className="arrow-btn">
              <img src={arrowup} alt="Logo" />
            </Button>
          </Tooltip>
          <Tooltip title="Arrow Up">
            <Button type="text" className="arrow-btn">
              <img src={arrowright} alt="arrowright" />
            </Button>
          </Tooltip>

          <Divider className="divider"></Divider>

          <Dropdown className="menu-dropdown" menu={{ items }} trigger={['click']}>
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                <img src={chart1} alt="chart" />

                <img src={arrowdown} alt="arrow down" />
              </Space>
            </a>
          </Dropdown>

          <Tooltip title="Calendar">
            <Button type="text" className="common-btn">
              <img src={union} alt="calender" />
              <Typography.Paragraph>Calendar</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Tooltip title="Chain">
            <Button type="text" className="common-btn">
              <img src={chain} alt="Chain" />
              <Typography.Paragraph>Chain</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Tooltip title="Capsule">
            <Button type="text" className="common-btn">
              <img src={capsule} alt="Capsule" />
              <Typography.Paragraph>Capsule</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Tooltip title="Compare">
            <Button type="text" className="common-btn">
              <img src={compare} alt="Compare" />
              <Typography.Paragraph>Compare</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Divider className="divider"></Divider>

          <Tooltip title="One Lane">
            <Button type="text" className="common-btn">
              <img src={lane} alt="One Lane" />
              <Typography.Paragraph>One Lane</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Tooltip title="One Y-Axis">
            <Button type="text" className="common-btn">
              <img src={yaxis} alt="One Y-Axis" />
              <Typography.Paragraph>One Y-Axis</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Tooltip title="Reset">
            <Button type="text" className="common-btn">
              <img src={reset} alt="Reset" />
              <Typography.Paragraph>Reset</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Tooltip title="Dimming">
            <Button type="text" className="common-btn">
              <img src={dimming} alt="Dimming" />
              <Typography.Paragraph>Dimming</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Tooltip title="Certainty">
            <Button type="text" className="common-btn">
              <img src={certainty} alt="Dimming" />
              <Typography.Paragraph>Certainty</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Divider className="divider"></Divider>

          <Dropdown className="label-dropdown" menu={{ items }} trigger={['click']}>
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                <div className="label-box">
                  <img src={label} alt="label" />

                  <img src={arrowdownlight} alt="arrow down" />
                </div>
                <Typography.Paragraph>Labels</Typography.Paragraph>
              </Space>
            </a>
          </Dropdown>

          <Tooltip title="Gridlines">
            <Button type="text" className="common-btn">
              <img src={gridlines} alt="Gridlines" />
              <Typography.Paragraph>Gridlines</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Dropdown className="label-dropdown" menu={{ items }} trigger={['click']}>
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                <div className="label-box">
                  <img src={summary} alt="label" />

                  <img src={arrowdownlight} alt="arrow down" />
                </div>
                <Typography.Paragraph>Summary</Typography.Paragraph>
              </Space>
            </a>
          </Dropdown>

          <Tooltip title="Tooltip">
            <Button type="text" className="common-btn">
              <img src={tooltip} alt="Tooltip" />
              <Typography.Paragraph>Tooltip</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Tooltip title="color">
            <Button type="text" className="common-btn">
              <img src={color} alt="color" />
              <Typography.Paragraph>Color</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Divider className="divider"></Divider>

          <Tooltip title="Zoom In">
            <Button type="text" className="common-btn">
              <img src={zoomin} alt="Zoom In" />
              <Typography.Paragraph>Zoom In</Typography.Paragraph>
            </Button>
          </Tooltip>

          <Tooltip title="Zoom Out">
            <Button type="text" className="common-btn">
              <img src={zoomout} alt="Zoom Out" />
              <Typography.Paragraph>Zoom Out</Typography.Paragraph>
            </Button>
          </Tooltip>
        </Space>

        <Typography.Link className="feedback-link" style={{ color: '#252963', display: 'flex', gap: '7px' }}>
          <img src={feedback} alt="feedback" />
          Feedback
        </Typography.Link>
      </div>
    </React.Fragment>
  );
}

export default Header;
