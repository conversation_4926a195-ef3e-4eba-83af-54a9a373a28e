import React, { useEffect, useState } from "react";
import { Collapse, Input, Tag, Typography, message } from "antd";
import { useAuth } from "../../../context/AuthContext";
import { AVAILABLE_DATA_SOURCES } from "../../../utils/constants";
import cross from '../../../img/cross.svg';

const { Panel } = Collapse;
const { Paragraph } = Typography;

export type DataSourceConfig = {
  [key: string]: string[];
};

interface DataSourceProps {
  systemName: string;
  dataSource: DataSourceConfig;
  onUpdate: (identifiers: DataSourceConfig) => void;
}

const DataSource: React.FC<DataSourceProps> = ({
  systemName,
  dataSource = {},
  onUpdate,
}) => {
  const user = useAuth();
  const isAuthorizedToEditConfiguration =
    user.authState.user?.role.alias === "process_engineer" ? false : true;

  const [dataSourceConfig, setDataSourceConfig] = useState<DataSourceConfig>(dataSource);
  const [inputValues, setInputValues] = useState<{ [key: string]: string }>({});
  useEffect(() => {
    const initialConfig: DataSourceConfig = dataSource || {};
    AVAILABLE_DATA_SOURCES.forEach((src) => {
      initialConfig[src] = dataSource[src] || [];
    });
    setDataSourceConfig(initialConfig);
  }, []);

  const handleAdd = (source: string) => {
    const value = inputValues[source]?.trim();
    if (!value) {
      message.warning("Please enter a valid value.");
      return;
    }

    if (dataSourceConfig[source]?.includes(value)) {
      message.warning("Value already exists.");
      return;
    }

    const updated = {
      ...dataSourceConfig,
      [source]: [...dataSourceConfig[source], value],
    };
    console.log("updated source config", updated);
    setDataSourceConfig(updated);
    setInputValues((prev) => ({ ...prev, [source]: "" }));
    onUpdate(updated);
  };

  const handleRemove = (source: string, removedValue: string) => {
    const updated = {
      ...dataSourceConfig,
      [source]: dataSourceConfig[source].filter((val) => val !== removedValue),
    };
    setDataSourceConfig(updated);
    onUpdate(updated);
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-2 mb-6">
        <span className="bg-blue-600 text-white px-2 py-1 rounded-full text-sm">
          Step 7/7
        </span>
        <h2 className="text-xl font-bold">Data Source</h2>
      </div>

      <Paragraph strong>Select Data Source</Paragraph>
      <Collapse accordion>
        {AVAILABLE_DATA_SOURCES.map((source) => (
          <Panel header={source.split("_").join(" ")} key={source}>
            <Input
              placeholder={`Enter custom identifier for ${source}`}
              value={inputValues[source] || ""}
              onChange={(e) =>
                setInputValues((prev) => ({
                  ...prev,
                  [source]: e.target.value,
                }))
              }
              onPressEnter={() => handleAdd(source)}
              disabled={isAuthorizedToEditConfiguration}
              className="border border-[#2684FF] rounded-md px-3 py-2 w-full"
            />
            {dataSourceConfig[source]?.length > 0 && (
              <div className="mb-8 mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Added Identifiers:
                </label>
                {dataSourceConfig[source]?.length > 0 && (
                  <div className="border border-gray-300 p-2 rounded flex items-center space-x-2 w-full ">
                    {dataSourceConfig[source].map((tag, index) => (
                      <div key={index} className="bg-gray-200 text-gray-800 px-2 py-0.5 flex items-center space-x-1">
                        <span className="text-xs">{tag}</span>
                        <img
                          src={cross}
                          onClick={() =>

                            handleRemove(source, tag)
                          }
                          alt="Close"
                          className="delete-icon h-2 cursor-pointer"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </Panel>
        ))}
      </Collapse>
    </div>
  );
};

export default DataSource;

