import React, { useState } from 'react'
import icon from '../../img/Icon.svg'
import { postRequest } from '../../utils/apiHandler';
import { useNavigate } from "react-router-dom";
import { validateInput } from '../../utils/validator';
import { useAuth } from '../../context/AuthContext';
import { message, Modal, Input } from 'antd';
import Email2FAAuthentication from '../Modal/EmailVerification';
import AuthenticatorAppVerification from '../Modal/AuthenticatorAppVerification';
const Login = () => {
    const { login } = useAuth();
    const navigate = useNavigate();
    const [formData, setFormData] = useState<any>({ email: "", password: "" });
    const [formError, setFormError] = useState<any>({ email: "", password: "" });
    const [userId, setUserId] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [userDetails , setUserDetails] = useState<any>(null)
    const [authStep , setAuthStep] =  useState<any>('signin')

    const handleChange = (e: any) => {
        const { id, value } = e.target;
        setFormData({ ...formData, [id]: value });
        setFormError({ ...formError, [id]: validateInput(id, value) });
    }

    const handleLogin = async (event: any) => {
        event.preventDefault();
        setIsLoading(true);

        // Validation
        Object.keys(formError).forEach((id) => {
            const error = validateInput(id, formData[id]);
            if (error) {
                setFormError((prevFormError: any) => ({
                    ...prevFormError,
                    [id]: error
                }));
            }
        });

        if (Object.values(formError).filter(err => err !== "").length > 0 || !formData.email || !formData.password) {
            setIsLoading(false);
            return;
        }

        try {
            const response = await postRequest('/auth/login', formData);
            const { status, message: apiMessage, data } = response.data;

            if (status === 200) {
                // Check if 2FA is required
                if (data.requiresTwoFactor) {
                    setUserId(data.userId);
                    setAuthStep('twoFactorAuthenticator')
                    setIsLoading(false);
                    setUserDetails(data)
                    return;
                }
                if(data?.requiresEmailVerification && !data.is_first_login){
                    setAuthStep('emailVerification')
                    setUserDetails(data)
                    return
                }

                // Normal login flow without 2FA
                message.success('Logged in successfully');
                await login(data.token, {
                    id: data.id,
                    first_name: data.first_name || "User",
                    last_name: data.last_name || "User",
                    onboarding: data.onboarding || "not_started",
                    role: data.role,
                    selected_systems: data.selected_systems || [],
                    tenant_id: data?.tenant_id,
                    is_first_login: data.is_first_login || false,
                });

                // If it's the first login, redirect to two-factor-auth setup
                if (data.is_first_login && data?.requiresEmailVerification) {

                    console.log(' 11111111111111111111111111111111111111111111111111111111:', );
                    navigate('/two-factor-auth');
                }
            } else {
                message.error(apiMessage);
            }
        } catch (error: any) {
            if (error.response) {
                message.error(error.response.data.message);
            } else {
                message.error(error.message);
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleTwoFactorVerify = async (otpValue:any) => {
        if (!otpValue || otpValue.length !== 6) {
            message.error('Please enter a valid 6-digit verification code');
            return;
        }

        setIsLoading(true);

        try {
            const response = await postRequest('/auth/login/verify-2fa', {
                userId: userId,
                token: otpValue
            });
            console.log('twoFactorCode :', otpValue);

            const { status, message: apiMessage, data } = response.data;

            if (status === 200) {
                message.success('Logged in successfully');

                await login(data.token, {
                    id: data.id,
                    first_name: data.first_name || "User",
                    last_name: data.last_name || "User",
                    onboarding: data.onboarding || "not_started",
                    role: data.role,
                    selected_systems: data.selected_systems || [],
                    tenant_id: data?.tenant_id,
                    is_first_login: data.is_first_login || false,
                });

                setAuthStep('signin')
            } else {
                message.error(apiMessage);
            }
        } catch (error: any) {
            if (error.response) {
                message.error(error.response.data.message);
            } else {
                message.error(error.message);
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleAnotherWayVerification = () =>{
        setAuthStep('emailVerification')
    }

    const handleAuthenticatorApp = () =>{
        setAuthStep('twoFactorAuthenticator')
    }

    const handleAuthLogin = async (userData:any) =>{
        await login(userData.token, {
            id: userData.id,
            first_name: userData.first_name || "User",
            last_name: userData.last_name || "User",
            onboarding: userData.onboarding || "not_started",
            role: userData.role,
            selected_systems: userData.selected_systems || [],
            tenant_id: userData?.tenant_id,
            is_first_login: userData.is_first_login || false,
          });

    }

    return (
        <React.Fragment>
            <div className="flex items-center justify-center min-h-screen bg-gray-100 p-4">
                <div className="w-full max-w-sm p-6 bg-white rounded-md shadow-md">
                    <div className="flex justify-center mb-8">
                        <img src={icon} className='h-20' alt='logo'/>
                    </div>
                  {(() => {
                        switch (authStep) {
                        case 'emailVerification':
                            return (
                                <Email2FAAuthentication
                                userData={userDetails}
                                handleVerified={(userData) => handleAuthLogin(userData)}
                                handleAnotherWay={handleAuthenticatorApp}
                                />
                            );

                        case 'twoFactorAuthenticator':
                            return (
                                <AuthenticatorAppVerification handleVerified={(otpValue) => {handleTwoFactorVerify(otpValue)}}
                                handleAnotherWayVerification={handleAnotherWayVerification}
                               />
                            );

                        case 'signin':
                        default:
                            return (
                                <div className="w-full h-full flex flex-col justify-center">
                                <h5 className='font-semibold mb-3'>Sign In with your email and password</h5>
                                    <form onSubmit={handleLogin}>
                                        <label htmlFor="email" className="block text-gray-700 font-medium mb-1">
                                            Email
                                        </label>
                                        <input
                                            type="email"
                                            id="email"
                                            value={formData.email}
                                            onBlur={handleChange}
                                            onChange={(e) => handleChange(e)}
                                            className="w-full p-2 mb-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
                                            placeholder="Enter your email"
                                        />
                                        <div className="text-red-700">{formError.email}</div>

                                        <label htmlFor="password" className="block text-gray-700 font-medium mb-1">
                                            Password
                                        </label>
                                        <input
                                            type="password"
                                            id="password"
                                            value={formData.password}
                                            onBlur={handleChange}
                                            onChange={(e) => handleChange(e)}
                                            className="w-full p-2 mb-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
                                            placeholder="Enter your password"
                                        />
                                        <div className="text-red-700">{formError.password}</div>

                                        <button
                                            type="submit"
                                            className="w-full py-2 bg-primary text-white rounded-md hover:bg-primary focus:outline-none focus:ring-2 focus:ring-primary"
                                        disabled={isLoading}
                                        >
                                            {isLoading ? 'Signing in...' : 'Sign in'}
                                        </button>
                                    </form>
                                    <button
                                        className="text-sm text-[#2d2253] hover:underline mt-4 w-full text-center"
                                        onClick={() => navigate('/forget-password')}
                                        >
                                        Forget Password
                                        </button>
                                </div>
                            );
                        }
                    })()}
                </div>
            </div>

            {/* <Modal
                title="Two-Factor Authentication"
                open={showTwoFactorModal}
                onCancel={() => setShowTwoFactorModal(false)}
                footer={[
                    <button
                        key="cancel"
                        onClick={() => setShowTwoFactorModal(false)}
                        className="px-4 py-2 mr-2 border border-gray-300 rounded-md"
                    >
                        Cancel
                    </button>,
                    <button
                        key="verify"
                        onClick={handleTwoFactorVerify}
                        className="px-4 py-2 bg-primary text-white rounded-md"
                        disabled={isLoading}
                    >
                        {isLoading ? 'Verifying...' : 'Verify'}
                    </button>
                ]}
            >
                <div className="py-4">
                    <p className="mb-4">
                        Please enter the verification code from your authenticator app.
                    </p>
                    <Input
                        type="text"
                        value={twoFactorCode}
                        onChange={(e) => setTwoFactorCode(e.target.value)}
                        placeholder="Enter 6-digit code"
                        maxLength={6}
                        className="text-center text-lg tracking-wider"
                        autoFocus
                    />
                </div>
            </Modal> */}
        </React.Fragment>
    );
}

export default Login;
