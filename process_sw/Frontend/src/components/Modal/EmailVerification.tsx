import React, { useState, useRef, useEffect, ChangeEvent, KeyboardEvent, ClipboardEvent } from 'react';
import { Modal, Button, Typography, message } from 'antd';
import { MailOutlined, LockOutlined } from '@ant-design/icons';
import { postRequest } from '../../utils/apiHandler';

const { Title, Text } = Typography;
const resendEmailOTPTime = 120
interface EmailOtpModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (userData:any) => void;
  email: string;
  userData:any;
  handleAnotherWay:() =>void;
}
interface Email2FAAuthenticationProps {
  userData: any; // adjust shape if userData has more fields
  handleVerified: (userData:any) => void;
  handleAnotherWay: () =>void
}

const EmailOtpModal: React.FC<EmailOtpModalProps> = ({ visible, onCancel, onSuccess, email ,userData , handleAnotherWay}) => {
  const [otp, setOtp] = useState<string[]>(['', '', '', '', '', '']);
  const [loading, setLoading] = useState<boolean>(false);
  const [timeLeft, setTimeLeft] = useState<number>(resendEmailOTPTime);
  const [resendDisabled, setResendDisabled] = useState<boolean>(true);
  const [codeSent, setCodeSent] = useState<boolean>(false); // NEW

  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);

  useEffect(() => {
    if (!visible || !codeSent) return;

    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          setResendDisabled(false);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [visible, codeSent]);

  const setUserDetails = (userData: any) =>{
    onSuccess(userData)
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>, index: number): void => {
    const value = e.target.value;

    if (value && !/^\d+$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value.slice(0, 1);
    setOtp(newOtp);

    if (value && index < 5 && inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number): void => {
    if (e.key === 'Backspace') {
      if (!otp[index] && index > 0) {
        const newOtp = [...otp];
        newOtp[index - 1] = '';
        setOtp(newOtp);
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>): void => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();

    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split('');
      setOtp(newOtp);
      inputRefs.current[5]?.focus();
    }
  };

  const sendVerificationCode = async (): Promise<void> => {
    setLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000)); // simulate API
      let user ={
        email:email,
        userData:userData
      }
      const response = await postRequest('/auth/send-otp',user)
      if(response.status==200) message.success('Verification code sent!');
      setCodeSent(true);
      setOtp(['', '', '', '', '', '']);
      setTimeLeft(resendEmailOTPTime);
      setResendDisabled(true);
    } catch (error) {
      message.error('Failed to send verification code.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async (): Promise<void> => {
    setLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000)); // simulate API
      let user ={
        email:email,
        userData:userData
      }
      const response = await postRequest('/auth/send-otp',user)
      if(response.status==200) message.success('A new OTP has been sent to your email');
        setOtp(['', '', '', '', '', '']);
        setTimeLeft(resendEmailOTPTime);
        setResendDisabled(true);
    } catch (error) {
      message.error('Failed to resend OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const verifyOtp = async () => {
    const otpValue = otp.join('');
  
    if (otpValue.length !== 6) {
      message.error('Please enter a complete 6-digit OTP');
      return;
    }
  
    const user = {
      userId:userData.userId,
      email: email,
      otp: otp,
    };
  
    setLoading(true);
    try {
      
      const response = await postRequest('/auth/verify-otp', user);
  
      console.log('✅ OTP verification response:', response);
  
      if (response.status === 200) {
        message.success('OTP verified successfully');
        setUserDetails(response?.data?.data);
        setLoading(false)
      } else {
        message.error(response?.data?.message || 'Failed to verify OTP. Please try again.');
        setLoading(false)
      }
    } catch (error: any) {
    console.log('error :', error?.response?.data.message);
    message.error(error?.response?.data.message || 'Failed to verify OTP. Please try again.');
    setLoading(false)
      
    }
  };
  

  useEffect(() => {
    if (visible && codeSent) {
      setTimeout(() => inputRefs.current[0]?.focus(), 100);
    }
  }, [visible, codeSent]);

  useEffect(() => {
    if (!visible) {
      setCodeSent(false);
      setOtp(['', '', '', '', '', '']);
      setTimeLeft(resendEmailOTPTime);
      setResendDisabled(true);
    }
  }, [visible]);

  return (
    <div className="p-1">
      <div className="flex items-center justify-center mb-4">
        <LockOutlined className="text-2xl text-blue-500 mr-2" />
        <Title level={4} className="m-0">Email Authentication</Title>
      </div>
  
      <div className="text-center">
  
        {!codeSent ? (
          <>
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
          <MailOutlined className="text-xl text-blue-500 mb-2" />
          <Text className="block mb-1">We will send a verification code to:</Text>
          <Text strong className="text-blue-600">
            {email || 'your registered email'}
          </Text>
        </div>
          <div className="mb-4">
            <Button 
              type="primary" 
              block 
              size="large" 
              onClick={sendVerificationCode} 
              loading={loading}
              className="h-12"
            >
              Send Verification Code
            </Button>
          </div>
          </>
        ) : (
            <form
            onSubmit={(e) => {
                e.preventDefault();
                verifyOtp();
            }}
            >
          <div className="px-2">
            <div className="mb-4">
              <div className="text-left mb-2">Enter 6-digit verification code</div>
              <div className="flex justify-between mb-4">
                {otp.map((digit, index) => (
                  <input
                    key={index}
                    ref={(el) => (inputRefs.current[index] = el)}
                    type="text"
                    value={digit}
                    onChange={(e) => handleChange(e, index)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    onPaste={index === 0 ? handlePaste : undefined}
                    className="w-12 h-12 text-center text-lg font-semibold 
                               border border-gray-300 rounded-lg
                               focus:border-blue-500 focus:ring-1 focus:ring-blue-500
                               focus:outline-none transition-all"
                    maxLength={1}
                    autoComplete="off"
                  />
                ))}
              </div>
            </div>
  
            <div className="flex justify-between items-center text-sm mb-6">
              <Text type="secondary">
                Time remaining: <span className="font-semibold">{formatTime(timeLeft)}</span>
              </Text>
              <Button 
                type="link" 
                disabled={resendDisabled} 
                onClick={handleResendOtp}
                className="p-0"
              >
                Resend Code
              </Button>
            </div>
  
            <div className="mb-4">
              {/* <Button 
                type="primary" 
                block 
                size="large" 
                onClick={verifyOtp} 
                loading={loading}
                className="h-12"
              >
                Verify and Continue
              </Button> */}
              <button
                        type="submit"
                        className="w-full py-2 bg-primary text-white rounded-md hover:bg-primary focus:outline-none focus:ring-2 focus:ring-primary">
                        Verify and Continue
                    </button>
            </div>
          </div>
        </form>
        )}
        {
          userData?.requiresTwoFactor ? <>
                    <div className="flex justify-end">
                <span className="text-blue-500 cursor-pointer" 
                onClick={handleAnotherWay}
                >
                 Try Authenticator App
                </span>
                </div>
          </>:<></>
        }
        <div className="text-center mt-4">
          <Text type="secondary" className="text-xs">
            Didn't receive the code? Check your spam folder or contact support.
          </Text>
        </div>
      </div>
    </div>
  );
  
};


const Email2FAAuthentication: React.FC<Email2FAAuthenticationProps> = ({ userData, handleVerified ,handleAnotherWay }) => {
  const handleSuccess = (userData:any): void => {
    handleVerified(userData); 
  };

  return (
    <div className="flex items-center justify-center p-1">
      <div className="w-full">
        <EmailOtpModal
          visible={true}
          onCancel={() => console.log("Cancelled the authentication")}
          onSuccess={handleSuccess}
          email={userData.email}
          userData={userData}
          handleAnotherWay={handleAnotherWay}
        />
      </div>
    </div>
  );
};

export default Email2FAAuthentication;
