import React from 'react';
import Plot from 'react-plotly.js';
import dayjs from 'dayjs';
import { Spin } from 'antd';

interface KpiData {
  id: number;
  system_id: number;
  name: string;
  ph: number;
  datetime: string;
}

interface KpiTrackingBarGraphProps {
  data: any[];
  loading: boolean;
  viewType: 'daily' | 'monthly';
}

export const KpiTrackingBarGraph: React.FC<KpiTrackingBarGraphProps> = ({ 
  data, 
  loading,
  viewType 
}) => {
  // Extract x and y values
  const xValues = data.map(item => item.date);
  const yValues = data.map(item => parseFloat(item.average_ph));

  // Calculate min and max values
  const minValue = Math.min(...yValues);
  const maxValue = Math.max(...yValues);
  
  // Calculate padding (10% of the range)
  const range = maxValue - minValue;
  const padding = range * 0.1;
  
  // Create Plotly data
  const plotData = [{
    type: 'bar',
    x: xValues,
    y: yValues,
    marker: {
      color: '#1a237e'
    },
    name: 'Average pH'
  }];
  
  // Create Plotly layout
  const layout = {
    title: {
      text: 'Daily Average pH Values',
      font: {
        size: 16,
        weight: 600
      }
    },
    height: 350,
    margin: {
      l: 60,
      r: 30,
      b: 60,
      t: 50,
      pad: 4
    },
    xaxis: {
      title: {
        text: 'Date',
        font: {
          size: 14,
          weight: 600
        }
      },
      tickangle: 0,
      tickfont: {
        size: 12
      },
      tickformat: '%b %d\n%Y'
    },
    yaxis: {
      title: {
        text: 'Average pH Value',
        font: {
          size: 14,
          weight: 600
        }
      },
      range: [Math.max(0, minValue - padding), maxValue + padding],
      tickformat: '.2f',
      tickfont: {
        size: 12
      }
    },
    bargap: 0.4,
    hovermode: 'closest'
  };
  
  // Plotly config
  const config = {
    responsive: true,
    displayModeBar: false,
    toImageButtonOptions: {
      format: 'png',
      filename: 'ph_tracking_chart',
      height: 350,
      width: 700,
      scale: 2
    }
  };

  return (
    <div className="w-full p-4 bg-white rounded-lg shadow-sm">
      {loading ? (
        <div className="flex justify-center items-center h-[350px]">
          <Spin size="large" />
        </div>
      ) : (
        <Plot
          data={plotData as any}
          layout={layout as any}
          config={config as any}
          style={{ width: '100%', height: '350px' }}
        />
      )}
    </div>
  );
}; 