import React, { useState } from 'react';
import ReactApex<PERSON>hart from 'react-apexcharts';
import moment from 'moment';
import { ApexOptions } from 'apexcharts';

interface TimeSeriesData {
  categories: string[];
  series: { name: string; data: number[] }[];
}

interface ParallelTimeSeriesChartProps {
  timeSeries?: TimeSeriesData; // Make timeSeries optional to avoid type errors
}

const ParallelTimeSeriesChart: React.FC<any> = ({ timeSeries }) => {
  const [selectedSeries, setSelectedSeries] = useState<number[]>([0]);
  
  if (!timeSeries || !timeSeries.categories.length || !timeSeries.series.length) {
    return <div>No data available</div>; // Fallback in case data is missing
  }

  const { series } = timeSeries;


  // Function to handle checkbox change
  const handleCheckboxChange = (index: number) => {
    if (selectedSeries.includes(index)) {
      setSelectedSeries(selectedSeries.filter((i) => i !== index)); // Remove the series if unchecked
    } else {
      setSelectedSeries([...selectedSeries, index]); // Add the series if checked
    }
  };

  // Function to generate chart options dynamically
  const getChartOptions = (name: string = '', index: number = 1): ApexOptions => ({
    chart: {
      toolbar: { show: true },
      zoom: { enabled: true },  // Enables zooming
      group: 'timeSeries',  // You can use chart grouping for synchronized charts
      id: `chart-${index}`,  // Dynamic chart ID
    },
    stroke: {
      width: 2,
      curve: 'smooth',  // Correct value for line chart stroke curve
    },
    grid: {
      padding: { right: 30, left: 20 },  // Customize grid padding
    },
    xaxis: {
      type: 'datetime',  // Properly define x-axis as datetime
      labels: {
        rotate: 0,  // Prevent rotation of labels
        style: { colors: '#000000' },  // Style for x-axis labels
        formatter: function (value: any) {
          return moment(value).format('DD MMM HH:mm:ss');  // Format x-axis labels
        },
      },
    },
    yaxis: {
      labels: {
        style: { colors: '#000000' },  // Style for y-axis labels
        formatter: (value: any) => value.toFixed(2),  // Format y-axis values
      },
    },
    tooltip: {
      x: { format: 'dd MMM yyyy HH:mm' },  // Format tooltip date
    },
  });
  

  // Render charts dynamically based on the series data
  return (
    <div className="flex flex-col gap-4 w-full h-full">
      {/* Render checkboxes */}
      {/* <div className="p-4 bg-gray-100 rounded-lg border">
        <div className="flex flex-col gap-4 p-4 bg-gray-100 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Select Charts</h3>
          <ul className="space-y-3">
            {series.map((serie, index) => (
              <li key={index} className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedSeries.includes(index)}
                  onChange={() => handleCheckboxChange(index)}
                  className="mr-3 h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <label className="text-gray-700 font-medium">{serie.name}</label>
              </li>
            ))}
          </ul>
        </div>
      </div> */}

      {/* Render charts based on selected checkboxes */}
      {series.map(
        (serie:any, index:any) =>
          selectedSeries.includes(index) && (
            <div key={index} className=" p-4 bg-[#F7F7F7] h-full">
              <h3 className="text-lg font-semibold mb-2">{serie.name}</h3>
              <ReactApexChart
                options={getChartOptions(serie.name, index)}
                series={[{ name: serie.name, data: serie.data }]}
                type="line"
                height={350}
              />
            </div>
          )
      )}
    </div>
  );
};

export default ParallelTimeSeriesChart;
