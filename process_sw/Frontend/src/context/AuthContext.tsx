import React, { createContext, useContext, useState, useEffect } from 'react';
import Cookies from "js-cookie";
import { AuthResult, OnboardingStatus } from '../routes/types';

interface AuthContextType {
  authState: AuthResult;
  loading: boolean;

  login: (token: string, userData: any) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>;
  updateFirstLoginStatus: (value: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [loading, setLoading] = useState(true);
  const [authState, setAuthState] = useState<AuthResult>({
    isAuthenticated: false,
    user: null
  });

  const checkAuth = async () => {
    try {
      console.log("Checking auth...");
      const token = Cookies.get('token');
      if (!token) {
        setAuthState({
          isAuthenticated: false,
          user: null
        });
        return;
      }

      const storedUserData = localStorage.getItem('userData');
      const userData = storedUserData ? JSON.parse(storedUserData) : null;

      console.log("User data from localStorage:", userData);

      setAuthState({
        isAuthenticated: true,
        user: userData || {
          id: '1',
          first_name: 'User',
          onboarding: 'not_started',
          is_first_login: false,
        }
      });
    } catch (error) {
      console.error('Auth check failed:', error);
      setAuthState({
        isAuthenticated: false,
        user: null
      });
    } finally {
      setLoading(false);
    }
  };

  // Add a function to update the is_first_login flag
  const updateFirstLoginStatus = (value: boolean) => {
    if (authState.user) {
      const updatedUser = {
        ...authState.user,
        is_first_login: value
      };

      // Update localStorage
      const userData = JSON.parse(localStorage.getItem('userData') || '{}');
      userData.is_first_login = value;
      localStorage.setItem('userData', JSON.stringify(userData));

      // Update state
      setAuthState({
        ...authState,
        user: updatedUser
      });
    }
  };


  const login = async (token: string, userData: any) => {
    try {
      Cookies.set('token', token);
      console.log("Login called, Token-", token);
      await new Promise((resolve) => setTimeout(resolve, 0));

      const onboardingStatus = userData.onboarding || 'not_started';
      localStorage.setItem(
        "userData",
        JSON.stringify({
          id: userData.id,
          first_name: userData.first_name,
          last_name: userData.last_name,
          onboarding: onboardingStatus,
          role: userData.role,
          tenant_id: userData.tenant_id,
          is_first_login: userData.is_first_login || false,
        })
      );

      setAuthState({
        isAuthenticated: true,
        user: {
          id: userData.id,
          first_name: userData.first_name || 'User',
          onboarding: onboardingStatus,
          role: userData.role,
          selected_systems: userData.selected_systems || [],
          tenant_id: userData.tenant_id,
          is_first_login: userData.is_first_login || false,
        },
      });
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = () => {
    Cookies.remove('token');
    Cookies.remove('chat_messages');
    localStorage.removeItem('userData');
    setAuthState({
      isAuthenticated: false,
      user: null
    });
  };

  useEffect(() => {
    checkAuth();
  }, []);

  return (
    <AuthContext.Provider value={{ authState, loading, login, logout, checkAuth, updateFirstLoginStatus }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};