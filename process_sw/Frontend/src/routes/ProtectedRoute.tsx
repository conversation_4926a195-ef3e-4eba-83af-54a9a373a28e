import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Header from '../components/Common/Header';
import Loader from '../components/Common/Loader';

const ProtectedRoute = () => {
  const { authState, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <Loader />
  }

  if (!authState.isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check if it's the user's first login
  // if (authState.user?.is_first_login && !location.pathname.includes('/two-factor-auth')) {
  //   return <Navigate to="/two-factor-auth" replace />;
  // }

  // Handle onboarding states
  const onboardingStatus = authState.user?.onboarding;
  const isOnboardingRoute = location.pathname.includes('/onboarding');

  if (onboardingStatus === 'completed' && isOnboardingRoute) {
    return <Navigate to="/" replace />;
  }

  if ((onboardingStatus === 'not_started' || onboardingStatus === 'in_progress') && !isOnboardingRoute && !location.pathname.includes('/two-factor-auth')) {
    return <Navigate to="/onboarding/industry" replace />;
  }

  if (location.pathname.includes('/admin/user-overview') && authState.user?.role?.alias != "admin") {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="App">
      <Header />
      <Outlet />
    </div>
  );
};

export default ProtectedRoute;