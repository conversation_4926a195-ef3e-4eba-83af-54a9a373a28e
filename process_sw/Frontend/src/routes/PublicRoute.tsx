import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Loader from '../components/Common/Loader';


const PublicRoute = ({ children }: { children: React.ReactNode }) => {
  const { authState, loading } = useAuth();

  if (loading) {
    return <Loader/>;
  }

  // If user is authenticated, redirect based on first login, onboarding status
  if (authState.isAuthenticated) {
    // If it's the user's first login, redirect to two-factor-auth setup
   if (authState.user?.onboarding) {
      return <Navigate to="/?tab=insight&workflowId=0" replace />;
    } else {
      return <Navigate to="/onboarding/industry" replace />;
    }
  }

  return <>{children}</>;
};

export default PublicRoute;