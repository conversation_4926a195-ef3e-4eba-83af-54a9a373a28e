import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Header from '../components/Common/Header';

const Protected = () => {
  const { authState, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!authState.isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // If user is authenticated but needs onboarding
  if (authState.isAuthenticated && !authState.user?.onboarding && !location.pathname.includes('/onboarding')) {
    console.log('Redirecting to onboarding...');
    return <Navigate to="/onboarding/industry" replace />;
  }

  // If user has completed onboarding but tries to access onboarding routes
  if (authState.user?.onboarding && location.pathname.includes('/onboarding')) {
    return <Navigate to="/" replace />;
  }

  return (
    <>
     <Header/>
     <Outlet />
    </>
   
  ) 
};

export default Protected;