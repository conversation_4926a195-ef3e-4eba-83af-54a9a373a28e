import <PERSON> from 'papaparse';
import readXlsxFile from 'read-excel-file';

export const parseCSV = (file: File, onComplete: (data: any) => void) => {
  Papa.parse(file, {
    header: true,
    skipEmptyLines: true,
    complete: function (result: any) {
      onComplete(result.data);
    },
  });
};

export const parseExcel = (file: File, onComplete: (data: any) => void) => {
  readXlsxFile(file).then((rows: any) => {
    const headers: any = rows[0];
    const rowData = rows.slice(1).map((row: any) => {
      const rowObject: any = {};
      headers.forEach((header: any, index: number) => {
        rowObject[header] = row[index];
      });
      return rowObject;
    });
    onComplete(rowData);
  });
};
