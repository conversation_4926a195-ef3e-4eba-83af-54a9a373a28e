# Pannel Implementation

This document outlines the implementation of the new `Pannel` system that allows users to save, manage, and reuse individual panel configurations independently from views.

## Overview

The implementation creates a new `pannels` table to store individual panel data with their configurations and data sources. This allows users to:

1. Save individual panels with their complete configuration
2. Manage saved panels independently from views
3. Reuse panels across different contexts
4. Organize panels by data source
5. Preview panel configurations before using them

## Database Changes

### New Table: `pannels`

```sql
CREATE TABLE pannels (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    configurations JSON NOT NULL,
    data_source_path UUID NULL,
    data_source VARCHAR(50) NOT NULL DEFAULT 'batch',
    user_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Columns:**
- `id`: Primary key
- `name`: Human-readable name for the panel
- `configurations`: JSON object containing all panel configuration data
- `data_source_path`: Reference to CSV file ID (foreign key to CSVFiles table)
- `data_source`: Type of data source (batch, stream, etc.)
- `user_id`: Reference to user who owns the panel

## Backend Implementation

### New Model: `Pannel`
- **File**: `process_sw/Backend/src/server/models/pannel.model.js`
- **Purpose**: Sequelize model for individual panels
- **Associations**: 
  - Belongs to User
  - Belongs to CSVFile (data source)

### New Controller: `Pannel`
- **File**: `process_sw/Backend/src/server/controllers/pannel.controllers.js`
- **Functions**:
  - `createPannel` - Create new panel
  - `getPannels` - Get all panels for user
  - `getPannelById` - Get specific panel
  - `updatePannel` - Update panel
  - `deletePannel` - Delete panel
  - `savePannelFromState` - Save panel from current state
  - `getPannelsByDataSource` - Get panels by CSV file

### New Routes: `Pannel`
- **File**: `process_sw/Backend/src/server/routes/pannel.routes.js`
- **Base Path**: `/api/v1/pannel`

### Updated App Configuration
- **File**: `process_sw/Backend/src/server/app.js`
- **Added**: Pannel routes to the application

## Frontend Implementation

### Updated Component: `PanelOptionsMenu`
- **File**: `process_sw/Frontend/src/components/Dashboard/Views Section/PanelOptionsMenu.tsx`
- **Changes**:
  - Added "Save as Pannel" option to dropdown menu
  - Added modal for saving panels
  - Added `savePanelAsPannel` function
  - Added state management for panel saving

### New Component: `PannelManager`
- **File**: `process_sw/Frontend/src/components/Dashboard/Views Section/PannelManager.tsx`
- **Purpose**: Comprehensive panel management interface
- **Features**:
  - Table view of all saved panels
  - Search and filter functionality
  - Filter by data source (all, current file, batch)
  - Edit panel names
  - Delete panels
  - Preview panel configurations
  - Use panels in current context

### Updated Component: `ViewSidebar`
- **File**: `process_sw/Frontend/src/components/Dashboard/Views Section/ViewSidebar.tsx`
- **Changes**:
  - Added settings button to open Pannel Manager
  - Integrated PannelManager component

## API Endpoints

### Pannel Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/pannel/create` | Create new panel |
| GET | `/api/v1/pannel/list` | Get all panels for user |
| GET | `/api/v1/pannel/:id` | Get specific panel |
| PUT | `/api/v1/pannel/:id` | Update panel |
| DELETE | `/api/v1/pannel/:id` | Delete panel |
| POST | `/api/v1/pannel/save-from-state` | Save panel from current state |
| GET | `/api/v1/pannel/data-source/:csvFileId` | Get panels by data source |

## Data Flow

### Saving Panel as Pannel
1. User clicks "Save as Pannel" in panel options menu
2. Modal opens for panel name input
3. Frontend sends panel data to `/pannel/save-from-state`
4. Backend creates new entry in `pannels` table with complete configuration
5. Panel is available for management and reuse

### Managing Panels
1. User clicks settings icon in Available Panels section
2. Pannel Manager opens showing all saved panels
3. User can search, filter, edit, delete, or preview panels
4. User can filter by current file to see relevant panels

### Using Saved Panels
1. User selects a panel from Pannel Manager
2. Panel configuration is applied to current context
3. Panel can be added to current view or used as template

## Configuration Structure

The `configurations` JSON field stores:

```json
{
  "panelType": "TimeSeriesPanel",
  "title": "Panel Title",
  "position": {
    "x": 0,
    "y": 0,
    "w": 6,
    "h": 6
  },
  "panelId": "unique-panel-id",
  "csvFileId": "csv-file-uuid",
  // ... other panel-specific configuration
}
```

## Key Features

### Panel Management
- **Save Individual Panels**: Save any panel with its complete configuration
- **Organize by Data Source**: Filter panels by CSV file or data source type
- **Search and Filter**: Find panels quickly by name or type
- **Preview Configuration**: View panel settings before using

### User Experience
- **Independent Management**: Panels are managed separately from views
- **Reusability**: Saved panels can be reused across different contexts
- **Data Source Awareness**: Panels remember their data source
- **User-Specific**: Each user has their own panel library

### Technical Benefits
- **Complete Configuration Storage**: All panel settings stored in one place
- **Flexible Data Structure**: JSON configuration allows for any panel type
- **Performance**: Efficient querying by user and data source
- **Scalability**: Independent table structure supports growth

## Differences from Panel Configurations

| Feature | Panel Configurations | Pannels |
|---------|---------------------|---------|
| **Purpose** | Reusable templates | Complete panel instances |
| **Scope** | Configuration only | Full panel with data source |
| **Usage** | Template for multiple views | Individual panel management |
| **Data Source** | Referenced separately | Integrated in configuration |
| **Position** | Stored separately | Included in configuration |

## Future Enhancements

1. **Panel Categories**: Organize panels by categories or tags
2. **Panel Templates**: Create template panels for common use cases
3. **Sharing**: Allow sharing panels between users
4. **Import/Export**: Import and export panel collections
5. **Panel Versioning**: Track changes to panel configurations
6. **Bulk Operations**: Select and manage multiple panels at once
7. **Panel Duplication**: Duplicate panels with modifications
8. **Advanced Filtering**: Filter by panel type, creation date, etc.

## Migration Notes

- This implementation is completely separate from existing view and panel configuration systems
- No existing functionality is affected
- Users can continue using existing workflows while adopting the new panel system
- The new system complements existing functionality rather than replacing it
